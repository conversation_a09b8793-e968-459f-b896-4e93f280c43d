/* My Orders Page Styles */

.my-orders-area {
  background-color: #f8f9fa;
}

/* Tab styles */
.order-tabs .nav-tabs {
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 25px;
}

.order-tabs .nav-link {
  border: none;
  font-weight: 500;
  color: #6c757d;
  padding: 12px 20px;
  margin-right: 5px;
  position: relative;
  transition: all 0.3s ease;
}

.order-tabs .nav-link:hover {
  color: #000;
}

.order-tabs .nav-link.active {
  color: #3577f0;
  background-color: transparent;
}

.order-tabs .nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #3577f0;
}

/* Table styles */
.table {
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
}

.table thead {
  background-color: #f8f9fa;
}

.table th {
  font-weight: 600;
  color: #212529;
  padding: 16px;
  border-bottom: 2px solid #dee2e6;
}

.table td {
  padding: 16px;
  vertical-align: middle;
}

.table tr:hover {
  background-color: #f8f9fa;
}

/* Badge styles */
.badge {
  padding: 6px 10px;
  font-weight: 500;
  border-radius: 4px;
}

/* Empty state */
.empty-orders {
  padding: 50px 0;
}

/* Pagination */
.pagination .page-link {
  color: #3577f0;
  border: 1px solid #dee2e6;
  margin: 0 3px;
  border-radius: 4px;
}

.pagination .page-item.active .page-link {
  background-color: #3577f0;
  border-color: #3577f0;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
}

/* Responsive styles */
@media (max-width: 767px) {
  .order-tabs .nav-link {
    padding: 10px 15px;
    font-size: 14px;
  }
  
  .table th,
  .table td {
    padding: 12px;
  }
} 