'use client'
import { useState, useEffect } from 'react'
import OptimizedImage from '@/components/common/OptimizedImage'
import ImagePreloader from '@/components/common/ImagePreloader'

const ImageTestPage = () => {
  const [loadTimes, setLoadTimes] = useState({})
  const [startTime, setStartTime] = useState(Date.now())

  // Sample images from dummyjson for testing
  const testImages = [
    'https://cdn.dummyjson.com/product-images/1/1.jpg',
    'https://cdn.dummyjson.com/product-images/2/1.jpg',
    'https://cdn.dummyjson.com/product-images/3/1.jpg',
    'https://cdn.dummyjson.com/product-images/4/1.jpg',
    'https://cdn.dummyjson.com/product-images/5/1.jpg',
    'https://cdn.dummyjson.com/product-images/6/1.jpg',
    'https://cdn.dummyjson.com/product-images/7/1.jpg',
    'https://cdn.dummyjson.com/product-images/8/1.jpg',
  ]

  useEffect(() => {
    setStartTime(Date.now())
  }, [])

  const handleImageLoad = (index) => {
    const loadTime = Date.now() - startTime
    setLoadTimes(prev => ({
      ...prev,
      [index]: loadTime
    }))
  }

  const averageLoadTime = Object.keys(loadTimes).length > 0 
    ? Object.values(loadTimes).reduce((a, b) => a + b, 0) / Object.values(loadTimes).length 
    : 0

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Preload critical images */}
      <ImagePreloader images={testImages.slice(0, 3)} priority={true} />
      
      <h1>Image Loading Performance Test</h1>
      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3>Performance Metrics:</h3>
        <p><strong>Images Loaded:</strong> {Object.keys(loadTimes).length} / {testImages.length}</p>
        <p><strong>Average Load Time:</strong> {averageLoadTime.toFixed(0)}ms</p>
        <p><strong>Page Load Started:</strong> {new Date(startTime).toLocaleTimeString()}</p>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2>Optimized Images (with lazy loading)</h2>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '20px',
          marginBottom: '20px'
        }}>
          {testImages.map((src, index) => (
            <div key={index} style={{ 
              border: '1px solid #ddd', 
              borderRadius: '8px', 
              overflow: 'hidden',
              backgroundColor: '#fff'
            }}>
              <div style={{ position: 'relative', height: '200px' }}>
                <OptimizedImage
                  src={src}
                  alt={`Test image ${index + 1}`}
                  fill={true}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  quality={80}
                  priority={index < 3}
                  onLoad={() => handleImageLoad(index)}
                />
              </div>
              <div style={{ padding: '10px' }}>
                <p style={{ margin: '0', fontSize: '14px', color: '#666' }}>
                  Image {index + 1}
                  {loadTimes[index] && (
                    <span style={{ color: '#28a745', fontWeight: 'bold' }}>
                      {' '}✓ {loadTimes[index]}ms
                    </span>
                  )}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2>Standard Images (for comparison)</h2>
        <p style={{ color: '#dc3545', fontSize: '14px' }}>
          ⚠️ These load without optimization - notice the difference!
        </p>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '20px' 
        }}>
          {testImages.slice(0, 4).map((src, index) => (
            <div key={index} style={{ 
              border: '1px solid #ddd', 
              borderRadius: '8px', 
              overflow: 'hidden',
              backgroundColor: '#fff'
            }}>
              <img
                src={src}
                alt={`Standard image ${index + 1}`}
                style={{ 
                  width: '100%', 
                  height: '200px', 
                  objectFit: 'cover' 
                }}
              />
              <div style={{ padding: '10px' }}>
                <p style={{ margin: '0', fontSize: '14px', color: '#666' }}>
                  Standard Image {index + 1}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div style={{ 
        marginTop: '40px', 
        padding: '20px', 
        backgroundColor: '#e7f3ff', 
        borderRadius: '8px',
        border: '1px solid #b3d9ff'
      }}>
        <h3>Optimization Features Demonstrated:</h3>
        <ul>
          <li>✅ <strong>Lazy Loading:</strong> Images load only when scrolled into view</li>
          <li>✅ <strong>Progressive Loading:</strong> Blur placeholder while loading</li>
          <li>✅ <strong>Priority Loading:</strong> First 3 images load immediately</li>
          <li>✅ <strong>Modern Formats:</strong> WebP/AVIF when supported</li>
          <li>✅ <strong>Responsive Images:</strong> Right size for your device</li>
          <li>✅ <strong>Error Handling:</strong> Fallback to placeholder on error</li>
          <li>✅ <strong>Caching:</strong> Subsequent loads are instant</li>
        </ul>
      </div>

      <div style={{ 
        marginTop: '20px', 
        padding: '15px', 
        backgroundColor: '#fff3cd', 
        borderRadius: '8px',
        border: '1px solid #ffeaa7'
      }}>
        <h4>How to Test:</h4>
        <ol>
          <li>Open browser DevTools (F12) → Network tab</li>
          <li>Refresh this page and watch the loading pattern</li>
          <li>Scroll down slowly to see lazy loading in action</li>
          <li>Check the file sizes in Network tab (WebP should be smaller)</li>
          <li>Refresh again to see cached loading (instant!)</li>
        </ol>
      </div>
    </div>
  )
}

export default ImageTestPage
