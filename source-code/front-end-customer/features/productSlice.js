import { createSlice } from "@reduxjs/toolkit"

const initialState = {
    latestJob: [],
    categoryList: [
        { id: 1, name: "<PERSON>", value: "kids", isChecked: false, },
        { id: 2, name: "<PERSON><PERSON>", value: "mens", isChecked: false, },
        { id: 3, name: "<PERSON><PERSON>", value: "womens", isChecked: false, }
    ],
    colorList: [
        { id: 1, name: "<PERSON>", value: "black", isChecked: false, },
        { id: 2, name: "<PERSON>", value: "blue", isChecked: false, },
        { id: 3, name: "<PERSON>", value: "gray", isChecked: false, },
        { id: 4, name: "<PERSON>", value: "green", isChecked: false, },
        { id: 5, name: "Red", value: "red", isChecked: false, }
    ],
    brandList: [
        { id: 1, name: "Adidas", value: "adidas", isChecked: false, },
        { id: 2, name: "<PERSON><PERSON><PERSON><PERSON>", value: "balenciaga", isChecked: false, },
        { id: 3, name: "Balmain", value: "balmain", isChecked: false, },
        { id: 4, name: "Burberry", value: "burberry", isChecked: false, },
        { id: 5, name: "Chloe", value: "chloe", isChecked: false, }
    ]
}

export const jobSlice = createSlice({
    name: "job",
    initialState,
    reducers: {
        clearCategoryToggle: (state) => {
            state?.categoryList?.map((item) => {
                item.isChecked = false
                return {
                    ...item,
                }
            })
        },
        categoryCheck: (state, { payload }) => {
            state?.categoryList?.map((item) => {
                if (item.id === payload) {
                    if (item.isChecked) {
                        item.isChecked = false
                    } else {
                        item.isChecked = true
                    }
                }
                return {
                    ...item,
                }
            })
        },

        clearColorToggle: (state) => {
            state?.colorList?.map((item) => {
                item.isChecked = false
                return {
                    ...item,
                }
            })
        },
        colorCheck: (state, { payload }) => {
            state?.colorList?.map((item) => {
                if (item.id === payload) {
                    if (item.isChecked) {
                        item.isChecked = false
                    } else {
                        item.isChecked = true
                    }
                }
                return {
                    ...item,
                }
            })
        },
        clearBrandToggle: (state) => {
            state?.brandList?.map((item) => {
                item.isChecked = false
                return {
                    ...item,
                }
            })
        },
        brandCheck: (state, { payload }) => {
            state?.brandList?.map((item) => {
                if (item.id === payload) {
                    if (item.isChecked) {
                        item.isChecked = false
                    } else {
                        item.isChecked = true
                    }
                }
                return {
                    ...item,
                }
            })
        },

    },
})

export const {
    clearCategoryToggle,
    categoryCheck,
    clearColorToggle,
    colorCheck,
    clearBrandToggle,
    brandCheck,
} = jobSlice.actions
export default jobSlice.reducer
