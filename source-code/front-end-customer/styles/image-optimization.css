/* Image Optimization Styles */

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Optimized image container */
.optimized-image-container {
  position: relative;
  overflow: hidden;
}

.optimized-image-container img {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Lazy loading fade-in effect */
.image-fade-in {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.image-fade-in.loaded {
  opacity: 1;
}

/* Product image hover effects */
.product-image-hover {
  transition: transform 0.3s ease;
}

.product-image-hover:hover {
  transform: scale(1.05);
}

/* Thumbnail navigation styles */
.thumbnail-nav {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 10px 0;
  scrollbar-width: thin;
}

.thumbnail-nav::-webkit-scrollbar {
  height: 4px;
}

.thumbnail-nav::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.thumbnail-nav::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.thumbnail-nav::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Thumbnail button styles */
.thumbnail-btn {
  flex-shrink: 0;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: border-color 0.3s ease, transform 0.2s ease;
}

.thumbnail-btn:hover {
  transform: translateY(-2px);
  border-color: #ddd;
}

.thumbnail-btn.active {
  border-color: #007bff;
  box-shadow: 0 0 0 1px #007bff;
}

/* Image error state */
.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  color: #6c757d;
  font-size: 14px;
  min-height: 200px;
}

/* Progressive image loading */
.progressive-image {
  position: relative;
}

.progressive-image .blur-placeholder {
  filter: blur(5px);
  transition: filter 0.3s ease;
}

.progressive-image .blur-placeholder.loaded {
  filter: blur(0);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .thumbnail-nav {
    justify-content: center;
  }
  
  .thumbnail-btn {
    width: 60px;
    height: 60px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .optimized-image-container img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Prefers reduced motion */
@media (prefers-reduced-motion: reduce) {
  .loading-skeleton,
  .image-fade-in,
  .product-image-hover,
  .thumbnail-btn {
    animation: none;
    transition: none;
  }
}
