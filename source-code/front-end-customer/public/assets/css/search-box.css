/* SearchBox Component Styles - Updated to match original header-search-bar */
.search-box-wrapper {
  position: relative;
  width: 100%;
}

.search-box-wrapper .search-info {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box-wrapper input[type="text"] {
  border: none;
  height: 50px;
  width: 100%;
  padding: 5px 45px 5px 45px;
  background-color: var(--tp-grey-2);
  border-radius: 6px;
  outline: none;
  transition: all 0.3s ease;
}

.search-box-wrapper input[type="text"]:focus {
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(255, 74, 23, 0.1);
}

.search-box-wrapper input[type="text"]::placeholder {
  color: var(--tp-grey-1);
}

.search-box-wrapper .header-search-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 20px;
  background: transparent;
  border: none;
  color: #666;
  cursor: pointer;
  z-index: 2;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.search-box-wrapper .header-search-icon:hover {
  color: #ff4a17;
}

/* Search Results Dropdown */
.search-results-dropdown {
  border-top: none !important;
  margin-top: -1px;
}

.search-result-item {
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-result-item:hover {
  background-color: #f8f9fa !important;
}

.search-result-item:last-child {
  border-bottom: none !important;
}

.search-loading {
  color: #666;
}

.search-no-results {
  font-style: italic;
}

.search-view-all a:hover {
  color: #e03d0f !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .search-box-wrapper {
    max-width: 100%;
  }
  
  .search-box-wrapper input[type="text"] {
    padding: 5px 40px 5px 40px;
    height: 45px;
  }
  
  .search-results-dropdown {
    left: -10px;
    right: -10px;
    max-height: 300px;
  }
  
  .search-result-item {
    padding: 10px 12px;
  }
  
  .search-result-item img {
    width: 40px !important;
    height: 40px !important;
  }
}

/* Ensure full width for header-search-bar */
.header-search-bar .search-box-wrapper {
  width: 100%;
}

/* For Header1 specific styling */
.header-meta-info .header-search-bar {
  width: 100%;
  flex: 1;
  margin-right: 40px;
  max-width: 500px;
}

/* For Header3 specific styling */
.green-logo-area .header-search-bar {
  width: 100%;
  flex: 1;
  margin-right: 40px;
  max-width: 500px;
}

/* Sticky header adjustments */
.header-sticky .search-box-wrapper input[type="text"] {
  height: 45px;
  padding: 5px 40px 5px 40px;
}

/* Sidebar search adjustments */
.tpsideinfo__search .search-box-wrapper {
  max-width: 100%;
}

.tpsideinfo__search .search-box-wrapper input[type="text"] {
  border-radius: 6px;
  padding: 10px 40px 10px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9e9e9;
}

/* Animation for dropdown */
.search-results-dropdown {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments for large screens */
@media only screen and (min-width: 1400px) {
  .header-meta-info .header-search-bar,
  .green-logo-area .header-search-bar {
    max-width: 600px;
  }
}

/* Responsive adjustments for medium screens */
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .header-meta-info .header-search-bar,
  .green-logo-area .header-search-bar {
    max-width: 450px;
    margin-right: 30px;
  }
}

/* Responsive adjustments for smaller large screens */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-meta-info .header-search-bar,
  .green-logo-area .header-search-bar {
    max-width: 350px;
    margin-right: 20px;
  }
} 