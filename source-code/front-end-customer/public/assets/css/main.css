@charset "UTF-8";
/*-----------------------------------------------------------------------------------

    Theme Name: Ninico - Minimal eCommerce HTML Template
    Author: Theme Pure
    Support: <EMAIL>
    Description: Template Name  – HTML5 Template
    Version: 1.0

-----------------------------------------------------------------------------------

/************ TABLE OF CONTENTS ***************

    01. THEME DEFAULT CSS
    02. HEADER CSS
    03. MEAN MENU CSS
	04. SLIDER CSS START
	05. CATEGORY CSS START
	06. PRODUCT CSS START
	07. SHOP CSS START
	08. BANNER CSS START
	09. MARQUE CSS START
	10. <PERSON>OR<PERSON>OLIO CSS START
	11. <PERSON><PERSON><PERSON><PERSON> CSS START
	12. CTA CSS START
	13. SERVICES CSS START
	14. BRAND CSS START
	15. PLATINAM-PRODUCT CSS START
	16. FEATURES CSS START
	17. TESTIMONIAL CSS START
	18. SELECTPRODUCT CSS START
	19. PRODUCT-DETAILS CSS START
	20. CART CSS START
	21. LOCATION CSS START
	22. SIGN CSS START
	23. COMING-SOON CSS START
	24. FOOTER CSS START


**********************************************/
/*----------------------------------------*/
/*  01. THEME DEFAULT CSS START
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Jost:wght@300;400;500;600;700display=swap");

:root {
    /**
  @font family declaration
  */
    --tp-ff-body: 'Jost', sans-serif;
    --tp-ff-fontawesome: "Font Awesome 5 Pro";
    /**
  @color declaration
  */
    --tp-common-white: #ffffff;
    --tp-common-black: #000;
    --tp-common-yellow: #ffcd00;
    --tp-heading-primary: #000;
    --tp-grey-1: #a5a5a6;
    --tp-grey-2: #f3f4f7;
    --tp-grey-3: #c8c8c8;
    --tp-grey-4: #e6dfd6;
    --tp-grey-5: #fff3f6;
    --tp-grey-6: #cfcfcf;
    --tp-grey-7: #d3d3d3;
    --tp-grey-8: #f6f6f6;
    --tp-grey-9: #999999;
    --tp-text-body: #040404;
    --tp-text-primary: #d51243;
    --tp-text-secondary: #777777;
    --tp-text-tertiary: #adadad;
    --tp-text-4: #a0a0a0;
    --tp-text-5: #999999;
    --tp-theme-1: #3D6CE7;
    --tp-theme-2: #f3eee7;
    --tp-theme-3: #f7f7f7;
    --tp-theme-4: #77977b;
    --tp-theme-5: #8e8b74;
    --tp-theme-6: #f8f8f8;
    --tp-theme-7: #ededed;
    --tp-theme-8: #f4f1ec;
    --tp-theme-9: #83b735;
    --tp-theme-10: #f3f4f7;
    --tp-theme-11: #f3f7f7;
    --tp-theme-12: #f0e2dd;
    --tp-theme-13: #f7efec;
    --tp-theme-14: #f58448;
    --tp-border-1: #e7e7e7;
    --tp-border-2: #f6f6f6;
    --tp-border-3: #ecf0ef;
    --tp-border-4: #e0e0e0;
    --tp-border-5: #282828;
    --tp-border-6: #e4e4e4;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

/*---------------------------------
	typography css start
---------------------------------*/
body {
    font-family: var(--tp-ff-body);
    font-size: 18px;
    font-weight: 400;
    color: var(--tp-text-body);
    line-height: 26px;
    overflow-x: hidden !important;
}

a {
    text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--tp-ff-body);
    color: var(--tp-header-1);
    margin-top: 0px;
    font-weight: var(--tp-fw-sbold);
    line-height: 1.2;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

h1 {
    font-size: 40px;
}

h2 {
    font-size: 32px;
}

h3 {
    font-size: 28px;
}

h4 {
    font-size: 24px;
}

h5 {
    font-size: 20px;
}

h6 {
    font-size: 16px;
}

ul {
    margin: 0px;
    padding: 0px;
}

p {
    font-family: var(--tp-ff-body);
    font-size: 18px;
    font-weight: 400;
    color: var(--tp-text-body);
    margin-bottom: 15px;
    line-height: 26px;
}

a,
.btn,
button,
p,
i,
input,
select,
textarea,
li,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

a:focus,
.button:focus {
    text-decoration: none;
    outline: none;
}

a:focus,
a:hover {
    color: inherit;
    text-decoration: none;
}

a {
    text-decoration: none;
}

a,
button {
    color: inherit;
    outline: none;
    border: none;
    background: transparent;
}

button:hover {
    cursor: pointer;
}

button:focus {
    outline: 0;
}

.uppercase {
    text-transform: uppercase;
}

.capitalize {
    text-transform: capitalize;
}

input {
    outline: none;
}

input[type=color] {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: none;
    border: 0;
    cursor: pointer;
    height: 100%;
    width: 100%;
    padding: 0;
    border-radius: 50%;
}

*::-moz-selection {
    background: var(--tp-common-black);
    color: var(--tp-common-white);
    text-shadow: none;
}

::-moz-selection {
    background: var(--tp-common-black);
    color: var(--tp-common-white);
    text-shadow: none;
}

::selection {
    background: var(--tp-text-primary);
    color: var(--tp-common-white);
    text-shadow: none;
}

*::-moz-placeholder {
    color: var(--tp-common-black);
    font-size: var(--tp-fz-body);
    opacity: 1;
}

*::placeholder {
    color: var(--tp-common-black);
    font-size: var(--tp-fz-body);
    opacity: 1;
}

/*---------------------------------
    common classes css start
---------------------------------*/
.w-img img {
    width: 100%;
}

.m-img img {
    max-width: 100%;
}

.fix {
    overflow: hidden;
}

.clear {
    clear: both;
}

.z-index-1 {
    z-index: 1;
}

.z-index-11 {
    z-index: 11;
}

.overflow-y-visible {
    overflow-x: hidden;
    overflow-y: visible;
}

.p-relative {
    position: relative;
}

.p-absolute {
    position: absolute;
}

.include-bg {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.gx-6 {
    --bs-gutter-x: 80px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px) {
    .gx-6 {
        --bs-gutter-x: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .gx-6 {
        --bs-gutter-x: 30px;
    }
}

.f-right {
    float: right;
}

/* scrollUp */
.scroll-top {
    width: 40px;
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: -10%;
    right: 50px;
    font-size: 16px;
    border-radius: 6px;
    z-index: 99;
    color: var(--tp-common-white);
    text-align: center;
    cursor: pointer;
    background: var(--tp-text-primary);
    transition: 1s ease;
    border: none;
}

@media (max-width: 767px) {
    .scroll-top {
        right: 30px;
        width: 40px;
        height: 40px;
        line-height: 40px;
    }
}

.scroll-top.open {
    bottom: 30px;
}

.scroll-top::after {
    position: absolute;
    z-index: -1;
    content: "";
    top: 100%;
    left: 5%;
    height: 10px;
    width: 90%;
    opacity: 1;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 80%);
}

.scroll-top:hover {
    background: var(--tp-text-body);
}

/*----------------------------------------*/
/*  Spacing
/*----------------------------------------*/
/*----------------------------------------*/
/*  00. BUTTON CSS START
/*----------------------------------------*/
.tp-btn {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    padding: 17px 33px;
    border-radius: 6px;
    color: var(--tp-text-body);
    background: var(--tp-common-white);
    line-height: 1.2;
}

.tp-btn:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-text-primary);
}

.tp-btn:hover i {
    animation: iconarrow 0.4s linear;
}

.tp-btn i {
    color: var(--tp-text-2);
    margin-left: 10px;
    text-transform: uppercase;
}

@media (max-width: 767px) {
    .tp-btn i {
        margin-left: 2px;
    }
}

.tpsecondary-btn {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    padding: 17px 33px;
    border-radius: 6px;
    background: var(--tp-text-primary);
    color: var(--tp-common-white);
    line-height: 1.2;
    position: relative;
}

.tpsecondary-btn::before {
    position: absolute;
    content: "";
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%);
    left: -10%;
    top: 0;
    height: 100%;
    width: 10%;
}

.tpsecondary-btn:hover::before {
    animation: lightwhite 0.8s;
}

.tpsecondary-btn:hover i {
    animation: iconarrow 0.4s linear;
}

.tpsecondary-btn i {
    color: var(--tp-text-2);
    margin-left: 10px;
    text-transform: uppercase;
}

.tpcart-btn,
.tpcheck-btn {
    border: 2px solid var(--tp-text-primary);
    display: block;
    justify-content: center;
    align-items: center;
    font-size: 13px;
    min-height: 45px;
    text-transform: uppercase;
    background: var(--tp-text-primary);
    color: var(--tp-common-white);
    border-radius: 30px;
    padding: 10px 30px;
    text-align: center;
    line-height: 1.5;
    padding: 14px;
    font-weight: 600;
}

.tpcart-btn:hover,
.tpcheck-btn:hover {
    background-color: #b50833;
    color: var(--tp-common-white);
    border: 2px solid #b50833;
}

.tpcart-btn {
    background-color: transparent;
    color: var(--tp-text-primary);
}

.tpcart-btn:hover {
    background-color: #b50833;
    color: var(--tp-common-white);
    border: 2px solid #b50833;
}

.tp-color-btn {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

/* 01.  ========= pulse effect animation ========== */
@-webkit-keyframes pulse {
    0% {
        -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }

    70% {
        -webkit-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@keyframes pulse {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }

    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }

    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

/* 02. ========= btn arrow animation ========== */
@keyframes iconarrow {
    49% {
        transform: translateX(30%);
    }

    50% {
        opacity: 0;
        transform: translateX(-30%);
    }

    51% {
        opacity: 1;
    }
}

/* 03. ========= btn arrow animation ========== */
@-webkit-keyframes shine {
    100% {
        left: 100%;
    }
}

@keyframes shine {
    100% {
        left: 100%;
    }
}

/* 04. ========= firefly ========== */
@keyframes firefly {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }

    100% {
        opacity: 1;
        color: white;
    }
}

/* 05. ========= firefly ========== */
@keyframes hotdeal {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.6;
    }

    75% {
        opacity: 0.8;
    }

    100% {
        opacity: 1.5;
    }
}

/* 06. ========= firefly ========== */
@keyframes lightwhite {
    100% {
        left: 100%;
    }
}

/* 07. ========= lighttitle ========== */
@keyframes lighttitle {
    100% {
        left: 100%;
    }
}

/* 08. ========= banneranimation ========== */
.banner-animation {
    position: relative;
    overflow: hidden;
}

.banner-animation::before {
    background: rgba(255, 255, 255, 0.3);
    bottom: 0;
    content: "";
    left: 50%;
    position: absolute;
    right: 50%;
    pointer-events: none;
    top: 0;
    opacity: 1;
    width: 0;
    transition: all 900ms linear;
}

.banner-animation:hover::before {
    left: 0;
    right: 0;
    opacity: 0;
    width: auto;
    transition: all 900ms linear;
}

/* 09. ========= banneranimation ========== */
@-webkit-keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 02. ========= video ripple animation ========== */
@keyframes tp-pulse-2 {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }

    70% {
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

/* 02. ========= section animation ========== */
@keyframes section-animation {
    0% {
        width: 0;
    }

    15% {
        width: 100%;
    }

    85% {
        opacity: 1;
    }

    90% {
        width: 100%;
        opacity: 0;
    }

    to {
        width: 0;
        opacity: 0;
    }
}

/*--- preloader ---*/
.dark #preloader {
    background-color: #232323;
}

#preloader {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f7f7;
    z-index: 999999;
}

.preloader {
    width: 50px;
    height: 50px;
    display: inline-block;
    padding: 0px;
    text-align: left;
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -25px;
    margin-top: -25px;
}

.preloader span {
    position: absolute;
    display: inline-block;
    width: 50px;
    height: 50px;
    border-radius: 100%;
    background: #D51243;
    -webkit-animation: preloader 1.3s linear infinite;
    animation: preloader 1.3s linear infinite;
}

.preloader span:last-child {
    animation-delay: -0.8s;
    -webkit-animation-delay: -0.8s;
}

@keyframes preloader {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }

    100% {
        transform: scale(1, 1);
        opacity: 0;
    }
}

@-webkit-keyframes preloader {
    0% {
        -webkit-transform: scale(0, 0);
        opacity: 0.5;
    }

    100% {
        -webkit-transform: scale(1, 1);
        opacity: 0;
    }
}

/*----------------------------------------*/
/*  00. PRELOADER CSS START
/*----------------------------------------*/
/*--
    - Background color
-----------------------------------------*/
.grey-bg {
    background: var(--tp-grey-1);
}

.grey-bg-2 {
    background: var(--tp-grey-2);
}

.grey-bg-3 {
    background: var(--tp-grey-8);
}

.white-bg {
    background: var(--tp-common-white);
}

.black-bg {
    background: var(--tp-common-black);
}

.space-bg {
    background: var(--tp-theme-2);
}

.theme-bg {
    background-color: var(--tp-theme-6);
}

.theme-bg-2 {
    background-color: var(--tp-text-primary);
}

.slider-bg {
    background-color: var(--tp-theme-8);
}

.footer-bg {
    background-color: var(--tp-theme-7);
}

.black-bg-2 {
    background-color: var(--tp-text-body);
}

.green-bg {
    background-color: var(--tp-theme-9);
}

.green-bg-2 {
    background-color: var(--tp-theme-11);
}

.slider-bg-2 {
    background-color: var(--tp-theme-10);
}

.platinam-bg {
    background-color: var(--tp-theme-12);
}

.platinam-light {
    background-color: var(--tp-theme-13);
}

/*----------------------------------------*/
/*  00. CAROUSEL CSS START
/*----------------------------------------*/
/*----------------------------------------*/
/*  00. OFF-CANVAS CSS START
/*----------------------------------------*/
/*----------------------------------------*/
/*  00. BREADCRUMB CSS START
/*----------------------------------------*/
.tp-breadcrumb__bg {
    background-position: right;
    background-repeat: repeat;
    background-size: cover;
}

@media (max-width: 767px) {
    .tp-breadcrumb__bg {
        background-position: left;
    }
}

.tp-breadcrumb__title {
    font-size: 44px;
    font-weight: 600;
    color: var(--tp-text-body);
}

@media (max-width: 767px) {
    .tp-breadcrumb__title {
        font-size: 36px;
    }
}

.tp-breadcrumb__link span {
    font-size: 16px;
    display: inline-block;
    color: var(--tp-text-secondary);
    font-weight: 400;
}

.tp-breadcrumb__link span a:hover {
    color: var(--tp-text-primary);
}

.tp-breadcrumb__link .breadcrumb-item-active {
    position: relative;
    color: var(--tp-text-body);
    padding-right: 40px;
}

.tp-breadcrumb__link .breadcrumb-item-active::before {
    position: absolute;
    content: "";
    height: 2px;
    width: 30px;
    background-color: #e4ded5;
    top: 50%;
    transform: translate(-50%);
    right: -13px;
}

.basic-pagination ul li {
    display: inline-block;
    list-style: none;
}

.basic-pagination ul li:not(:last-child) {
    margin-right: 5px;
}

.basic-pagination ul li a,
.basic-pagination ul li span {
    display: inline-block;
    width: 50px;
    height: 50px;
    line-height: 48px;
    text-align: center;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    border: 1px solid var(--tp-theme-7);
    font-size: 14px;
    font-weight: 600;
    color: var(--tp-text-body);
    -webkit-transition: all 0.2s ease-out 0s;
    -moz-transition: all 0.2s ease-out 0s;
    -ms-transition: all 0.2s ease-out 0s;
    -o-transition: all 0.2s ease-out 0s;
    transition: all 0.2s ease-out 0s;
}

@media (max-width: 767px) {

    .basic-pagination ul li a,
    .basic-pagination ul li span {
        height: 30px;
        width: 30px;
        line-height: 28px;
        font-size: 12px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

    .basic-pagination ul li a,
    .basic-pagination ul li span {
        height: 40px;
        width: 40px;
        line-height: 36px;
        font-size: 14px;
    }
}

.basic-pagination ul li a:hover,
.basic-pagination ul li a.current,
.basic-pagination ul li span:hover,
.basic-pagination ul li span.current {
    background: var(--tp-text-primary);
    border-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

.basic-pagination ul li a i,
.basic-pagination ul li span i {
    font-weight: 600;
}

/*----------------------------------------*/
/*  00. ACCORDIONS CSS START
/*----------------------------------------*/
/*----------------------------------------*/
/*  00. TAB CSS START
/*----------------------------------------*/
.tpnavbar {
    display: flex;
    justify-content: flex-end;
}

@media (max-width: 767px) {
    .tpnavbar {
        justify-content: flex-start;
    }
}

.tpnavbar .nav-tabs {
    border-bottom: none;
}

.tpnavbar .nav-link {
    border: none;
    margin: 0;
    padding: 0;
    font-size: 16px;
    font-weight: 600;
    margin-left: 50px;
    color: var(--tp-text-body);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpnavbar .nav-link {
        margin-left: 20px;
    }
}

@media (max-width: 767px) {
    .tpnavbar .nav-link {
        margin-left: 0;
        margin-right: 20px;
        margin-bottom: 30px;
    }
}

.tpnavbar .nav-link.active {
    color: var(--tp-text-primary);
    text-decoration: underline;
}

.tpproductnav {
    justify-content: center;
}

@media (max-width: 767px) {
    .tpproductnav {
        justify-content: flex-start;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpproductnav.tpnavbar .nav-link {
        margin-left: 20px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpproductnav.tpnavbar .nav-link {
        margin-left: 30px;
    }
}

.tpproductall {
    text-align: right;
}

@media (max-width: 767px) {
    .tpproductall {
        text-align: left;
        margin-bottom: 30px;
    }
}

.tpproductall a {
    font-weight: 600;
    font-size: 16px;
}

.tpproductall a:hover {
    color: var(--tp-text-primary);
}

.tpproductall a i {
    margin-left: 8px;
}

/*----------------------------------------*/
/*  00. MODAL CSS START
/*----------------------------------------*/
/*----------------------------------------*/
/*   SECTION-TITLE CSS START
/*----------------------------------------*/
.tpsection__title {
    font-size: 26px;
    color: var(--tp-text-body);
    font-weight: 600;
}

@media (max-width: 767px) {
    .tpsection__title {
        font-size: 22px;
    }
}

.tpsection__title span {
    position: relative;
    display: inline-block;
    font-weight: 300;
    color: var(--tp-text-primary);
    font-size: 26px;
    letter-spacing: -1px;
}

.tpsection__title span img {
    position: absolute;
    left: 0;
    bottom: 1px;
    z-index: -1;
    animation: section-animation 2s infinite;
}

.tpsectionarea__subtitle {
    Font-size: 20px;
    font-weight: 400;
    color: var(--tp-text-primary);
    margin-bottom: 0;
}

.tpsectionarea__title {
    font-size: 40px;
    font-weight: 600;
    color: var(--tp-text-body);
}

.tpsectionarea__title i {
    transform: translateY(5px);
}

.solid-line {
    position: relative;
    border-top: 1px solid var(--tp-border-1);
}

.solid-line .tpsection__title {
    transform: translateY(-18px);
    background: #f8f8f8;
    display: inline-block;
    padding: 0 30px;
}

/*----------------------------------------*/
/*  00. HEADER CSS START
/*----------------------------------------*/
.header-welcome-text {
    padding: 7px 0;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .header-welcome-text {
        text-align: center;
    }
}

.header-welcome-text span {
    font-size: 16px;
    font-weight: 600;
}

@media (max-width: 767px) {
    .header-welcome-text span {
        font-size: 14px;
    }
}

.header-welcome-text a {
    color: var(--tp-text-primary);
    font-weight: 600;
    display: inline-block;
    position: relative;
}

@media (max-width: 767px) {
    .header-welcome-text a {
        font-size: 14px;
    }
}

.header-welcome-text a::before {
    position: absolute;
    content: "";
    height: 1px;
    width: 100%;
    background-color: var(--tp-text-primary);
    bottom: 0;
}

.header-welcome-text a i {
    margin-left: 10px;
}

.header-welcome-text a:hover i {
    animation: iconarrow 0.4s linear;
}

.header-search-bar {
    width: 100%;
}

.header-search-bar .header-search-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 20px;
}

.header-search-bar input {
    border: none;
    height: 50px;
    width: 100%;
    padding: 5px 45px 5px 45px;
    background-color: var(--tp-grey-2);
    border-radius: 6px;
}

.header-search-bar input::placeholder {
    color: var(--tp-grey-1);
}

.header-meta {
    margin-left: 40px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .header-meta {
        margin-left: 0;
    }
}

@media (max-width: 767px) {
    .header-meta {
        transform: translateY(4px);
    }
}
.header-meta__value select {
	height: 50px;
	background: #fff;
	border: 1px solid var(--tp-border-1);
	border-radius: 4px;
	padding: 0 10px;
}

.header-meta__lang>ul>li {
    position: relative;
    list-style: none;
}

.header-meta__lang>ul>li>a {
    border: 1px solid var(--tp-border-1);
    display: flex;
    align-items: center;
    padding: 6px 19px 6px 6px;
    border-radius: 5px;
    margin-right: 20px;
    font-weight: 700;
    font-size: 14px;
}

.header-meta__lang>ul>li>a img {
    margin-right: 12px;
}

.header-meta__lang>ul>li>a i {
    color: var(--tp-grey-3);
    font-size: 15px;
    margin-left: 8px;
}

.header-meta__lang>ul>li:hover .header-meta__lang-submenu {
    opacity: 1;
    visibility: visible;
    top: 100%;
}

.header-meta__lang>ul>li:hover a i {
    transform: rotate(180deg);
    color: var(--tp-text-primary);
}

.header-meta__lang-submenu {
    position: absolute;
    top: 110%;
    left: 0px;
    width: 140px;
    background: var(--tp-common-white);
    z-index: 9;
    box-shadow: 0 30px 70px 6px rgba(11, 6, 70, 0.08);
    padding: 15px 20px;
    border-radius: 4px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.header-meta__lang-submenu li {
    list-style: none;
    margin-bottom: 15px;
}

.header-meta__lang-submenu li:last-child {
    margin-bottom: 0;
}

.header-meta__lang-submenu li:hover {
    color: var(--tp-text-primary);
}

.header-meta__value span {
    font-weight: 700;
    font-size: 14px;
}

.header-meta__value .nice-select {
    height: 50px;
    line-height: 47px;
    width: 95px;
}

.header-meta__value .nice-select:focus {
    border-color: var(--tp-text-primary);
}

.header-meta__social a {
    margin-left: 25px;
    font-size: 20px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .header-meta__social a {
        margin-left: 15px;
    }
}

@media (max-width: 767px) {
    .header-meta__social a {
        margin-left: 15px;
        font-size: 17px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .header-meta__social a {
        margin-left: 25px;
    }
}

.header-meta__social a::hover {
    color: var(--tp-common-black);
}

.header-meta__search-5 {
    position: relative;
}

.header-meta__search-5 input {
    height: 50px;
    width: 200px;
    background-color: transparent;
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
    padding: 2px 10px 2px 45px;
}

.header-meta__search-5 input:focus {
    border: 1px solid var(--tp-text-primary);
}

.header-meta__search-5 input::placeholder {
    font-size: 14px;
    color: var(--tp-text-5);
}

.header-meta__search-5 .header-search-icon-5 {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 18px;
    color: var(--tp-text-body);
    font-size: 14px;
}

.header-cart span {
    display: inline-block;
    height: 15px;
    width: 15px;
    line-height: 15px;
    border-radius: 50%;
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
    font-size: 12px;
    text-align: center;
    position: absolute;
    top: -4px;
    right: -8px;
}

.cat-menu__category>a {
    display: flex;
    align-items: center;
    padding: 9px 25px;
    min-height: 50px;
    min-width: 215px;
    font-size: 16px;
    color: var(--tp-common-white);
    font-weight: 600;
    border-radius: 6px 6px 0 0;
    background: var(--tp-text-primary);
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .cat-menu__category>a {
        min-width: 205px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px) {
    .cat-menu__category>a {
        min-width: 100%;
    }
}

.cat-menu__category>a i {
    margin-right: 15px;
}

.cat-menu__category .category-menu {
    background: var(--tp-common-white);
    width: 100%;
    border: 1px solid var(--tp-border-2);
    border-top: none;
    position: absolute;
    top: 100%;
    left: 0;
    border-radius: 0 0 6px 6px;
    z-index: 9;
}

.cat-menu__category .category-menu .daily-offer {
    padding: 14px 0;
    background-color: var(--tp-border-2);
    border-radius: 0 0 6px 6px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px),
only screen and (min-width: 1200px) and (max-width: 1399px) {
    .cat-menu__category .category-menu .daily-offer {
        padding: 14px 0;
    }
}

.cat-menu__category .category-menu .daily-offer ul li {
    list-style: none;
    padding: 3px 20px 3px 25px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .cat-menu__category .category-menu .daily-offer ul li {
        padding: 0px 20px 0px 25px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .cat-menu__category .category-menu .daily-offer ul li {
        padding: 3px 20px 3px 15px;
    }
}

.cat-menu__category .category-menu .daily-offer ul li a {
    background-image: linear-gradient(#d51243, #d51243), linear-gradient(#d51243, #d51243);
    display: inline;
    background-size: 0% 1px, 0 1px;
    background-position: 100% 100%, 0 90%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    font-size: 14px;
    font-weight: 600;
}

.cat-menu__category .category-menu .daily-offer ul li a:hover {
    color: var(--tp-text-primary);
    background-size: 0 1px, 100% 1px;
}

.cat-menu__list {
    padding: 10px 0;
}

.cat-menu__list li {
    list-style: none;
}

.cat-menu__list li>a {
    display: flex;
    color: var(--tp-text-body);
    font-size: 14px;
    font-weight: 400;
    padding: 9px 25px 5px 25px;
    background: transparent;
    align-items: center;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    position: relative;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .cat-menu__list li>a {
        padding: 9px 15px 5px 15px;
    }
}

.cat-menu__list li>a:hover {
    color: var(--tp-text-primary);
}

.cat-menu__list li>a:hover::after {
    color: var(--tp-text-primary);
}

.cat-menu__list li>a i {
    width: 32px;
    color: var(--tp-text-primary);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .cat-menu__list li>a i {
        width: 28px;
    }
}

.cat-menu__list li.menu-item-has-children:hover .submenu {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

.cat-menu__list li.menu-item-has-children>a::after {
    content: "\f178";
    position: absolute;
    right: 20px;
    font-size: 14px;
    font-family: "Font Awesome 5 Pro";
    font-weight: 700;
    color: var(--tp-grey-3);
}

.cat-menu__list li.menu-item-has-children>a:hover::after {
    color: var(--tp-text-primary);
}

.cat-menu__list li.menu-item-has-children .submenu {
    position: absolute;
    width: 100%;
    left: 100%;
    right: 0;
    top: 0;
    bottom: 0;
    padding-left: 0;
    padding-top: 1.25rem;
    list-style: none;
    background-color: var(--tp-common-white);
    border-left: 1px solid var(--tp-border-1);
    border-top: 0;
    white-space: nowrap;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    -webkit-transition: all 0.25s cubic-bezier(0.28, 0.12, 0.22, 1);
    transition: all 0.25s cubic-bezier(0.28, 0.12, 0.22, 1);
}

.main-menu ul>li {
    position: relative;
    list-style: none;
    display: inline-block;
    margin-right: 65px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .main-menu ul>li {
        margin-right: 50px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .main-menu ul>li {
        margin-right: 22px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .main-menu ul>li {
        margin-right: 40px;
    }
}

.main-menu ul>li>a {
    position: relative;
    font-weight: 600;
    font-size: 14px;
    color: var(--tp-text-body);
    padding: 15px 0 23px 0;
    display: block;
    line-height: 1;
}

.main-menu ul>li.has-dropdown>a {
    position: relative;
}

.main-menu ul>li.has-dropdown>a::after {
    transform: translateY(2px);
    content: "\f107";
    font-size: 14px;
    color: var(--tp-grey-3);
    font-family: var(--tp-ff-fontawesome);
    margin-left: 4px;
    display: inline-block;
    font-weight: 400;
}

.main-menu ul>li .submenu {
    position: absolute;
    top: 120%;
    left: 0;
    min-width: 250px;
    padding: 15px 0;
    background-color: var(--tp-common-white);
    z-index: 2;
    transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1), visibility 0.4s cubic-bezier(0.19, 1, 0.22, 1), transform 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    box-shadow: 0px 8px 20px rgba(61, 110, 168, 0.1);
    border-radius: 0px 0px 10px 10px;
    border-top: 2px solid var(--tp-text-primary);
    opacity: 0;
    visibility: hidden;
    transition: 0.1s;
}

.main-menu ul>li .submenu li {
    display: block;
    width: 100%;
    margin: 0;
}

.main-menu ul>li .submenu li.has-dropdown>a::after {
    position: absolute;
    top: 50%;
    right: 25px;
    -webkit-transform: translateY(-50%) rotate(-90deg);
    -moz-transform: translateY(-50%) rotate(-90deg);
    -ms-transform: translateY(-50%) rotate(-90deg);
    -o-transform: translateY(-50%) rotate(-90deg);
    transform: translateY(-50%) rotate(-90deg);
}

.main-menu ul>li .submenu li a {
    padding: 12px 25px;
    font-size: 15px;
    z-index: 1;
    color: var(--tp-common-black);
    width: 100%;
    font-weight: 500;
}

.main-menu ul>li .submenu li a::before {
    display: none;
}

.main-menu ul>li .submenu li .submenu {
    left: 120%;
    top: 0;
    visibility: hidden;
    opacity: 0;
}

.main-menu ul>li .submenu li:hover>a {
    color: var(--tp-text-primary);
    margin-left: 8px;
}

.main-menu ul>li .submenu li:hover .mega-menu-title {
    margin-left: 0;
}

.main-menu ul>li .submenu li:hover>.submenu {
    left: 100%;
    visibility: visible;
    opacity: 1;
}

.main-menu ul>li .mega-menu {
    padding: 20px 40px 20px 40px;
    top: 120%;
    width: 800px;
    background-color: var(--tp-common-white);
    left: 0;
    right: 0;
    z-index: 5;
    transform-origin: top;
    flex: 0 0 auto;
    display: flex;
    margin: 0 auto;
    box-shadow: 0px 8px 20px rgba(61, 110, 168, 0.1);
    border-radius: 0px 0px 10px 10px;
    border-top: 2px solid var(--tp-text-primary);
    -webkit-transition: all 0.1s ease-out 0s;
    -moz-transition: all 0.1s ease-out 0s;
    -ms-transition: all 0.1s ease-out 0s;
    -o-transition: all 0.1s ease-out 0s;
    transition: all 0.1s ease-out 0s;
}

.main-menu ul>li .mega-menu li {
    width: 260px;
    text-align: left;
    padding-left: 0;
    padding-right: 0;
}

.main-menu ul>li .mega-menu li .mega-menu-title {
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    font-size: 13px;
    position: relative;
    text-transform: uppercase;
    color: var(--tp-common-black);
    margin-bottom: 10px;
    padding-left: 0;
}

.main-menu ul>li .mega-menu li ul {
    padding-left: 0;
    text-align: left;
}

.main-menu ul>li .mega-menu li ul li {
    padding-left: 0;
    text-align: left;
}

.main-menu ul>li .mega-menu li ul li a {
    position: relative;
    padding: 10px;
    font-size: 16px;
    font-weight: 400;
    color: var(--tp-text-body);
    text-transform: capitalize;
    text-align: start;
    margin-right: 8px;
    padding-left: 0;
}

.main-menu ul>li .mega-menu li ul li a:hover {
    color: var(--tp-text-primary);
}

.main-menu ul>li:hover>a {
    color: var(--tp-text-primary);
}

.main-menu ul>li:hover>a::after {
    color: var(--tp-text-primary);
}

.main-menu ul>li:hover>a::before {
    width: 41px;
}

.main-menu ul>li:hover>.submenu {
    top: 100%;
    visibility: visible;
    opacity: 1;
    z-index: 99;
}

.menu-contact {
    display: flex;
    justify-content: flex-end;
}

.menu-contact ul li {
    display: inline-block;
    margin-right: 40px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .menu-contact ul li {
        margin-right: 22px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .menu-contact ul li {
        margin-right: 12px;
    }
}

.menu-contact ul li:last-child {
    margin-right: 0;
}

.menu-contact__item {
    display: flex;
    align-items: center;
}

.menu-contact__icon {
    color: var(--tp-text-primary);
    font-size: 15px;
    margin-right: 8px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .menu-contact__icon {
        margin-right: 4px;
    }
}

.menu-contact__info a {
    font-weight: 600;
    font-size: 14px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .menu-contact__info a {
        font-size: 12px;
    }
}

.menu-contact__info a:hover {
    color: var(--tp-text-primary);
}

@media (max-width: 767px) {
    .headertoplag {
        justify-content: center !important;
    }
}

.headertoplag__lang>ul>li {
    position: relative;
    list-style: none;
}

.headertoplag__lang>ul>li>a {
    display: inline-block;
    padding: 6px 19px 8px 6px;
    margin-right: 10px;
    font-weight: 700;
    font-size: 14px;
}

.headertoplag__lang>ul>li>a:hover {
    color: var(--tp-text-primary);
}

.headertoplag__lang>ul>li>a img {
    margin-right: 5px;
}

.headertoplag__lang>ul>li>a i {
    color: var(--tp-grey-3);
    font-size: 14px;
}

.headertoplag__lang>ul>li:hover .header-meta__lang-submenu {
    opacity: 1;
    visibility: visible;
    top: 100%;
}

.headertoplag__lang>ul>li:hover a i {
    transform: rotate(180deg);
    color: var(--tp-text-primary);
}

.menu-top-social a {
    font-size: 14px;
    margin-left: 10px;
}

.menu-top-social a:hover {
    color: var(--tp-text-primary);
}

.mainmenu__search-bar input {
    border: none;
    height: 50px;
    width: 200px;
    padding: 5px 20px 5px 45px;
    background-color: var(--tp-grey-2);
    border-radius: 6px;
    color: var(--tp-text-body);
}

.mainmenu__search-bar input::placeholder {
    font-size: 14px;
    color: rgba(153, 153, 153, 0.6);
}

.mainmenu__search-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 20px;
}

.mainmenu__main {
    margin-left: 120px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .mainmenu__main {
        margin-left: 50px;
    }
}

.mainmenu__main>.main-menu a {
    padding: 45px 0;
}

.mainmenu__main>.main-menu>nav>ul>li:nth-child(3) {
    margin-right: 230px;
}

.mainmenu__logo {
    position: absolute;
    top: 48%;
    left: 47%;
    transform: translateY(-52%);
}

.tertiary-header-top .headertoplag__lang ul li:hover i {
    transform: rotate(0);
}

.tertiary-header-top .headertoplag__lang ul li a:hover i {
    color: var(--tp-text-primary);
}

.tertiary-header-top .headertoplag__lang ul li a:hover.order-tick i {
    animation: iconarrow 0.4s linear;
    color: var(--tp-text-primary);
}

.tertiary-header-top .headertoplag__lang ul li a i {
    color: var(--tp-text-body);
    margin-right: 6px;
}

.tertiary-main-menu {
    border-top: 1px solid #efefef;
}

.tertiary-main-menu .main-menu ul li a {
    padding: 21px 0 20px 0;
}

.tertiary-main-menu .cat-menu__category>a {
    border-radius: 0px;
    min-height: 55px;
}

.tertiary-main-menu .category-menu {
    background-color: var(--tp-common-white);
    z-index: 99;
}

.coupon-offer {
    padding: 15px 25px 15px 25px;
    background-color: var(--tp-text-body);
}

.coupon-offer span {
    color: #868686;
    font-size: 14px;
}

.coupon-offer span a {
    color: var(--tp-common-white);
    text-decoration: underline;
    font-weight: 600;
}

.coupon-offer span a:hover {
    color: var(--tp-text-primary);
}

.coupon-offer i {
    text-align: end;
    color: #535353;
}

.coupon-offer i:hover {
    color: var(--tp-text-primary);
}

.platinam-header-top .header-welcome-text span {
    font-size: 14px;
    font-weight: 400;
}

.platinam-header-top .header-welcome-text b {
    font-size: 14px;
    font-weight: 600;
}

.platinam-menuarea .mainmenu__search-bar input {
    background-color: transparent;
}

.platinam-menuarea .mainmenu__main {
    margin-left: 0px;
}

.header-canvas {
    flex: 0 0 auto;
    margin-right: 15px;
    line-height: 1;
    margin-top: 10px;
    font-size: 22px;
}

.menu-area-4 {
    display: flex;
    justify-content: center;
}

.red-header-top .menu-top-social a {
    font-size: 16px;
    color: var(--tp-common-white);
    margin-left: 45px;
}

.red-header-top .menu-top-social a:first-child {
    margin-left: 0;
}

.red-header-top .menu-top-social a:hover {
    opacity: 0.8;
}

.red-header-top .header-welcome-text {
    font-size: 16px;
    color: var(--tp-common-white);
    font-weight: 400;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .red-header-top .header-welcome-text {
        text-align: center !important;
    }
}

@media (max-width: 767px) {
    .red-header-top .header-welcome-text {
        text-align: start !important;
    }
}

.red-header-top .header-welcome-text a {
    color: var(--tp-common-white);
    text-decoration: underline;
}

.category-style-five .category-menu {
    position: static;
}

.mainmenu-5 .main-menu a {
    padding: 45px 0;
}

.tpsideinfo {
    background: var(--tp-text-body);
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    padding: 30px;
    width: 350px;
    transform: translateX(-120%);
    transition: 0.3s;
    z-index: 999;
    overflow-y: scroll;
}

@media (max-width: 767px) {
    .tpsideinfo {
        width: 275px;
    }
}

.tpsideinfo.tp-sidebar-opened {
    transform: translateX(0);
}

.tpsideinfo__close {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    color: var(--tp-common-white);
    width: 100%;
    display: block;
    min-height: 45px;
    text-transform: uppercase;
    font-size: 13px;
    font-weight: 600;
    background-color: var(--tp-text-primary);
}

.tpsideinfo__search-title {
    color: var(--tp-common-white);
    font-size: 13px;
    font-weight: 500;
    text-transform: uppercase;
}

.tpsideinfo__search form {
    position: relative;
    padding-top: 13px;
    padding-bottom: 20px;
}

.tpsideinfo__search form input {
    width: 100%;
    height: 45px;
    border-radius: 3px;
    font-size: 14px;
    border: 1px solid transparent;
    background: #F3F3F9;
    padding: 10px 20px;
    padding-right: 45px;
}

.tpsideinfo__search form input::placeholder {
    color: var(--tp-border-3);
}

.tpsideinfo__search button {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.tpsideinfo__nabtab .nav-link.active {
    border: 0;
    padding: 10px 15px;
    position: relative;
    text-transform: uppercase;
    font-size: 13px;
    font-weight: 500;
    border-radius: 3px;
    background-color: var(--tp-text-primary);
}

.tpsideinfo__nabtab .nav-link {
    border: 0;
    padding: 10px 15px;
    position: relative;
    text-transform: uppercase;
    font-size: 13px;
    font-weight: 500;
    border-radius: 3px;
    color: var(--tp-common-black);
    background-color: var(--tp-common-white);
}

.tpsideinfo__nabtab button {
    width: 100%;
}

.tpsideinfo__nabtab .nav li {
    display: inline-block;
    width: 49%;
    margin-right: 4px;
}

.tpsideinfo__nabtab .nav li:last-child {
    margin-right: 0;
}

.tpsideinfo__nabtab .mega-menu-title {
    color: var(--tp-common-white);
    font-size: 13px;
    text-transform: uppercase;
    padding-top: 5px;
}

.tpsideinfo__nabtab .mean-container .mean-nav ul {
    background-image: none !important;
}

.tpsideinfo__nabtab .home-menu-style li {
    width: 50% !important;
}

.tpsideinfo__nabtab .home-menu-style li a {
    font-size: 13px !important;
    font-weight: 400 !important;
}
.tp-shop-selector {
    display: flex;
}
.input-range__slider {
    background-color: #bb2d3b !important;
}
.tpsideinfo__account-link,
.tpsideinfo__wishlist-link {
    padding-top: 5px;
    padding-bottom: 5px;
}
a.wishlist, a.add-to-cart {
    cursor: pointer;
}
.tpsideinfo__account-link a,
.tpsideinfo__wishlist-link a {
    font-size: 16px;
    color: var(--tp-common-white);
    font-weight: 500;
}

.tpsideinfo__account-link a:hover,
.tpsideinfo__wishlist-link a:hover {
    color: var(--tp-text-primary);
}

.tpsideinfo__account-link a:hover i,
.tpsideinfo__wishlist-link a:hover i {
    color: var(--tp-text-primary);
}

.tpsideinfo__account-link a i,
.tpsideinfo__wishlist-link a i {
    font-size: 18px;
    color: var(--tp-common-white);
    line-height: 35px;
    text-align: center;
    margin-right: 10px;
}

.tpsidebar-categories ul li {
    list-style: none;
    display: block;
    text-align: left;
    list-style: none;
    text-transform: capitalize;
    padding: 5px 0px 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tpsidebar-categories ul li a {
    color: var(--tp-common-white);
    display: block;
}

.tpsidebar-categories ul li a:hover {
    color: var(--tp-text-primary);
}

.body-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    height: 100%;
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 99;
    right: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-out 0s;
}

.body-overlay.opened {
    opacity: 1;
    visibility: visible;
}

.tp-cart-info-area.tp-sidebar-opened {
    transform: translateX(0);
}

.tp-sidebar-close {
    color: var(--tp-common-white);
    position: absolute;
    left: -35px;
    font-size: 21px;
    background: #171151;
    width: 35px;
    height: 35px;
}

.tpcartinfo {
    background-color: var(--tp-common-white);
    text-align: start;
    position: fixed;
    right: 0;
    top: 0;
    height: 100%;
    box-shadow: rgba(5, 13, 54, 0.05) 5px 15px 30px 0px;
    transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    z-index: 9999999;
    width: 380px;
    transform: translateX(100%);
}

@media (max-width: 767px) {
    .tpcartinfo {
        width: 280px;
    }
}

.tpcartinfo .tp-shop-sidebar-opened {
    transform: translateX(0);
}

.tpcart__close {
    color: var(--tp-text-primary);
    right: 30px;
    font-size: 18px;
    width: 35px;
    height: 35px;
    position: absolute;
    top: 8px;
    z-index: 2;
}

@media (max-width: 767px) {
    .tpcart__close {
        width: 30px;
        height: 30px;
    }
}

.cartbody-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    height: 100%;
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 99;
    left: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-out 0s;
}

.cartbody-overlay.opened {
    opacity: 1;
    visibility: visible;
}

.tpcart {
    float: none;
    height: 100%;
    overflow: hidden;
    position: relative;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex: 1 1 auto;
    align-items: stretch;
    flex-direction: column;
}

.tpcart__product {
    position: relative;
    display: flex;
    flex: 1 1 auto;
    align-items: stretch;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    padding: 0 30px;
}

.tpcart__title {
    padding: 16px 30px;
    background-color: #F8F8F8;
    font-weight: 700;
    font-size: 15px;
    color: var(--tp-text-body);
    text-transform: uppercase;
    margin-bottom: 20px;
}

.tpcart ul li {
    list-style: none;
    padding-top: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--tp-border-1);
}

.tpcart__item {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
}

.tpcart__img {
    margin-right: 20px;
    position: relative;
}

@media (max-width: 767px) {
    .tpcart__img {
        margin-right: 4px;
    }
}

.tpcart__img img {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    object-fit: cover;
}

.tpcart__del {
    position: absolute;
    color: var(--tp-text-primary);
    left: 0;
    top: 0;
}

.tpcart__content-title {
    font-size: 14px;
    font-weight: 400;
    color: var(--tp-text-body);
}

.tpcart__content-title a:hover {
    color: var(--tp-text-primary);
}

.tpcart__cart-price {
    font-weight: 600;
    font-size: 12px;
    color: var(--tp-text-body);
}

.tpcart__cart-price .new-price {
    color: var(--tp-text-primary);
}

.tpcart__total-price {
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    color: var(--tp-text-primary);
    margin-bottom: 25px;
    padding-top: 25px;
    border-top: 1px solid var(--tp-border-1);
}

.tpcart__total-price .heilight-price {
    font-size: 18px;
    font-weight: 700;
}

.tpcart__free-shipping {
    padding: 13px 30px;
    background-color: var(--tp-theme-5);
}

.tpcart__free-shipping span {
    color: var(--tp-common-white);
    font-size: 14px;
}

@media (max-width: 767px) {
    .tpcart__free-shipping span {
        font-size: 12px;
    }
}

.tpcart__free-shipping span b {
    font-weight: 600;
    text-transform: uppercase;
}

.tpcart__checkout {
    margin-bottom: 30px;
}

.header-sticky {
    position: fixed;
    left: 0;
    margin: auto;
    top: 0;
    width: 100%;
    box-shadow: 0 0 60px 0 rgba(0, 0, 0, 0.07);
    z-index: 99;
    animation: 300ms ease-in-out 0s normal none 1 running fadeInDown;
    background: var(--tp-common-white);
}

.header-sticky .tp-bt-btn {
    box-shadow: rgba(100, 100, 111, 0.1) 1px 5px 20px 1px;
}

.tp-home-one.header-sticky {
    padding-top: 0px;
    padding-bottom: 0px;
}

.tp-home-one.header-sticky .main-menu nav>ul>li>a {
    padding: 40px 0;
}

.tp-mobile-header-area.header-sticky {
    position: fixed;
    left: 0;
    margin: auto;
    top: 0;
    width: 100%;
    box-shadow: 0 0 60px 0 rgba(0, 0, 0, 0.07);
    z-index: 99;
    animation: 300ms ease-in-out 0s normal none 1 running fadeInDown;
    background: var(--tp-common-white);
}

.tp-mobile-header-area.header-sticky .tp-bt-btn {
    box-shadow: rgba(100, 100, 111, 0.1) 1px 5px 20px 1px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .header-brand {
        margin-left: 40px;
    }
}

.tp-sticky-one {
    display: none;
}

.tp-sticky-one.header-sticky {
    display: block;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .tp-sticky-one.header-sticky {
        display: none;
    }
}

/*----------------------------------------*/
/*  15. MEANMENU CSS START
/*----------------------------------------*/
/* mean menu customize */
.mean-container a.meanmenu-reveal {
    display: none;
}

.mean-container .mean-nav {
    background: none;
    margin-top: 0;
}

.mean-container .mean-bar {
    padding: 0;
    min-height: auto;
    background: none;
}

.mean-container .mean-nav>ul {
    padding: 0;
    margin: 0;
    width: 100%;
    list-style-type: none;
    display: block !important;
}

.mean-container a.meanmenu-reveal {
    display: none !important;
}

.mean-container .mean-nav ul li a {
    width: 100%;
    padding: 10px 0;
    color: #fff;
    font-size: 16px;
    line-height: 1.5;
    font-weight: 500;
    text-transform: capitalize !important;
}

.mean-container .mean-nav ul li a:hover {
    color: var(--tp-text-primary);
}

.mean-container .mean-nav ul li a.mean-expand {
    margin-top: 8px;
    padding: 0 !important;
    line-height: 14px;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    height: 26px;
    width: 26px;
    line-height: 26px;
    color: #fff;
    top: 0;
    font-weight: 400;
}

.mean-container .mean-nav ul li a.mean-expand:hover {
    background: var(--clr-theme-1);
    color: var(--tp-text-primary);
    border-color: var(--clr-theme-1);
}

.mean-container .mean-nav ul li>a>i {
    display: none;
}

.mean-container .mean-nav ul li>a.mean-expand i {
    display: inline-block;
    font-size: 14px;
}

.mean-container .mean-nav>ul>li:first-child>a {
    border-top: 0;
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked {
    color: var(--tp-heading-secondary);
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked i {
    transform: rotate(45deg);
    color: var(--tp-text-primary);
}

.mean-container .mean-nav ul .home-menu-style li a {
    text-align: center;
}

.mean-container .mean-nav ul li a:hover img {
    transform: scale(0.92);
}

.mean-container .mean-nav ul li a img {
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    width: 100%;
    margin-bottom: 5px;
}

/*----------------------------------------*/
/*  04. SLIDER CSS START
/*----------------------------------------*/
.tp-slide-item {
    position: relative;
}

@media (max-width: 767px) {
    .tp-slide-item {
        margin-bottom: 20px;
    }
}

.tp-slide-item__img img {
    border-radius: 6px;
    max-width: 100%;
}

.tp-slide-item__content {
    position: absolute;
    z-index: 1;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    padding-left: 60px;
    max-width: 60%;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-slide-item__content {
        padding-left: 35px;
        max-width: 70%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-slide-item__content {
        padding-left: 30px;
        max-width: 70%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-slide-item__content {
        padding-left: 35px;
    }
}

@media (max-width: 767px) {
    .tp-slide-item__content {
        padding-left: 15px;
        max-width: 80%;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-slide-item__content {
        padding-left: 15px;
        max-width: 50%;
    }
}

.tp-slide-item__sub-title {
    font-size: 20px;
    font-weight: 400;
    color: var(--tp-text-primary);
    display: block;
    margin-bottom: 5px;
}

@media (max-width: 767px) {
    .tp-slide-item__sub-title {
        font-size: 16px;
    }
}

.tp-slide-item__title {
    color: var(--tp-text-body);
    font-size: 50px;
    font-weight: 600;
    line-height: 1.2;
}

@media (max-width: 767px) {
    .tp-slide-item__title {
        font-size: 18px;
        margin-bottom: 10px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-slide-item__title {
        font-size: 26px;
    }
}

.tp-slide-item__title i {
    font-style: normal;
    color: var(--tp-text-primary);
    position: relative;
}

.tp-slide-item__title i img {
    position: absolute;
    left: -6px;
    bottom: 10px;
    z-index: -1;
    animation: section-animation 3s infinite;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .tp-slide-item__thumb img {
        max-width: 100%;
    }
}

.tp-slider-area .slider-pagination {
    position: absolute;
    bottom: 15px;
    z-index: 9;
    left: 0;
    margin: 0 auto;
    right: 0;
    text-align: center;
}

@media (max-width: 767px) {
    .tp-slider-area .slider-pagination {
        margin-bottom: 10px;
    }
}

.tp-slider-area .slider-pagination .swiper-pagination-bullet {
    width: 25px;
    height: 4px;
    display: inline-block;
    border-radius: 5px;
    background: var(--tp-common-black);
    margin: 0 5px;
    opacity: 0.1;
}

.tp-slider-area .slider-pagination .swiper-pagination-bullet-active {
    background: var(--tp-text-primary);
    opacity: 1;
}

.swiper-slide-active .tp-slide-item__sub-title,
.swiper-slide-active .tp-slide-item__title,
.swiper-slide-active .tp-slide-item__slide-btn,
.swiper-slide-active .tpslidertwo__sub-title,
.swiper-slide-active .tpslidertwo__title,
.swiper-slide-active .tpslidertwo__content p,
.swiper-slide-active .tpslidertwo__slide-btn {
    animation-fill-mode: both;
    animation-name: fadeInUp;
}

.swiper-slide-active .tpslidertwo__img {
    animation-fill-mode: both;
    animation-name: fadeInRight;
}

.swiper-slide-active .tp-slide-item__sub-title,
.swiper-slide-active .tpslidertwo__sub-title {
    animation-delay: 0.6s;
    animation-duration: 0.8s;
}

.swiper-slide-active .tp-slide-item__title,
.swiper-slide-active .tpslidertwo__title {
    animation-delay: 0.8s;
    animation-duration: 1s;
}

.swiper-slide-active .tp-slide-item__slide-btn {
    animation-delay: 1s;
    animation-duration: 1.2s;
}

.swiper-slide-active .tpslidertwo__content p {
    animation-delay: 1s;
    animation-duration: 1.2s;
}

.swiper-slide-active .tpslidertwo__slide-btn {
    animation-delay: 1.4s;
    animation-duration: 1.3s;
}

.swiper-slide-active .tpslidertwo__img {
    animation-delay: 1s;
    animation-duration: 1s;
}

.tpslidertwo__item {
    margin-top: -70px;
}

@media (max-width: 767px) {
    .tpslidertwo__item {
        margin-top: 0;
        margin-left: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpslidertwo__img>img {
        width: 360px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpslidertwo__img-shape img {
        height: 100px;
        width: 100px;
    }
}

.tpslider-banner:hover .tpslider-banner__img {
    border-radius: 6px;
}

.tpslider-banner:hover .tpslider-banner__img img {
    transform: scale(1.05);
    border-radius: 6px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpslider-banner {
        margin-bottom: 20px;
    }
}

.tpslider-banner__content {
    position: absolute;
    top: 0;
    left: 0;
    padding: 30px 10px 10px 30px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpslider-banner__content {
        padding: 15px 10px 10px 20px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslider-banner__content {
        padding: 15px 10px 10px 15px;
    }
}

.tpslider-banner__sub-title {
    font-size: 16px;
    font-weight: 400;
    color: var(--tp-text-primary);
}

.tpslider-banner__title {
    font-size: 20px;
    font-weight: 600;
    color: var(--tp-text-body);
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpslider-banner__title br {
        display: none;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslider-banner__title {
        font-size: 16px;
    }
}

.tpslider-banner__img {
    margin-left: -11px;
    position: relative;
    width: 100%;
    border-radius: 6px;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    overflow: hidden;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpslider-banner__img {
        margin-top: 20px;
        margin-left: 0;
    }
}

@media (max-width: 767px) {
    .tpslider-banner__img {
        margin-left: 0;
    }
}

.tpslider-banner__img img {
    border-radius: 6px;
    width: 100%;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpslider-banner__img img {
        height: 204px;
        object-fit: cover;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslider-banner__img img {
        height: 170px;
        object-fit: cover;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpslider-banner__img img {
        width: 100%;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpslider-banner__img.tpbannerthumb-5 img {
        height: 178px;
        object-fit: cover;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslider-banner__img.tpbannerthumb-5 img {
        height: 185px;
        object-fit: cover;
    }
}

.tpslidertwo__img {
    margin-right: 180px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslidertwo__img {
        margin-right: 0;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslidertwo__img img {
        max-width: 100%;
    }
}

.tpslidertwo__img-shape {
    position: absolute;
    bottom: 160px;
    right: -150px;
    animation: rotate 15s linear infinite;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslidertwo__img-shape {
        bottom: 120px;
        right: 0px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpslidertwo__img-shape {
        bottom: 50px;
        right: 0px;
    }
}

.tpslidertwo__content p {
    font-size: 18px;
    color: var(--tp-text-secondary);
    margin-bottom: 45px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslidertwo__content p {
        margin-bottom: 20px;
    }
}

@media (max-width: 767px) {
    .tpslidertwo__item {
        height: 380px;
    }
}

.tpslidertwo__sub-title {
    font-size: 20px;
    color: var(--tp-text-primary);
    margin-bottom: 0;
}

.tpslidertwo__title {
    font-size: 60px;
    font-weight: 600;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpslidertwo__title {
        font-size: 48px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpslidertwo__title {
        font-size: 48px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpslidertwo__title {
        font-size: 40px;
    }
}

@media (max-width: 767px) {
    .tpslidertwo__title {
        font-size: 26px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpslidertwo__title {
        font-size: 48px;
    }
}

@media (max-width: 767px) {
    .tpslider-btn-4 {
        margin-right: 8px;
    }
}

.slider-pagination-2 .slidertwo_pagination {
    position: absolute;
    bottom: 55px;
    z-index: 9;
    left: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .slider-pagination-2 .slidertwo_pagination {
        bottom: 15px;
    }
}

@media (max-width: 767px) {
    .slider-pagination-2 .slidertwo_pagination {
        margin-bottom: 10px;
    }
}

.slider-pagination-2 .slidertwo_pagination .swiper-pagination-bullet {
    width: 25px;
    height: 4px;
    display: inline-block;
    border-radius: 5px;
    background: var(--tp-grey-1);
    margin: 0 5px;
}

.slider-pagination-2 .slidertwo_pagination .swiper-pagination-bullet-active {
    background: var(--tp-text-primary);
}

@media (max-width: 767px) {
    .tp-slide-item__slide-btn.tp-btn {
        font-size: 12px;
        padding: 10px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-slide-item__slide-btn.tp-btn {
        font-size: 14px;
        padding: 14px;
    }
}

.secondary-slider__item {
    background-color: var(--tp-theme-10);
}

.secondary-slider .tpslidertwo__img-shape {
    bottom: 376px;
    right: -210px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .secondary-slider .tpslidertwo__img-shape {
        bottom: 0;
        right: 0;
    }
}

.secondary-slider .tpslidertwo__slide-btn span {
    font-size: 14px;
    color: var(--tp-text-secondary);
}

.secondary-slider .tpslidertwo__slide-btn span b {
    font-size: 18px;
    color: var(--tp-text-body);
    display: block;
}

.greenslider-pagination {
    position: absolute;
    bottom: 55px;
    z-index: 9;
    left: 0;
    right: 0;
    text-align: center;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .greenslider-pagination {
        bottom: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .greenslider-pagination {
        bottom: 15px;
    }
}

.greenslider-pagination .swiper-pagination-bullet {
    width: 25px;
    height: 4px;
    display: inline-block;
    background: transparent;
    margin: 0 5px;
    height: 30px;
    width: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    position: relative;
}

.greenslider-pagination .swiper-pagination-bullet::before {
    position: absolute;
    content: "";
    height: 8px;
    width: 8px;
    background-color: var(--tp-text-body);
    top: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
    left: 50%;
}

.greenslider-pagination .swiper-pagination-bullet-active {
    background: transparent;
    border: 1px solid #d7d9e0;
}

.platinamborder {
    border: 15px solid #fff;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .platinamborder .tpslidertwo__item {
        margin-left: 5px;
    }
}

.platinam-slidershape {
    position: absolute;
    top: 65px;
    right: -80px;
    z-index: 2;
    animation: rotate 15s linear infinite;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .platinam-slidershape {
        top: 10px;
        right: -50px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .platinam-slidershape {
        right: -25px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .platinam-slidershape {
        top: -30px;
        right: -45px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .tpsliderthree__img {
        padding-top: 0;
    }
}

.tpsliderthree__img img {
    width: 100%;
}

.tpsliderthree__pagination {
    position: absolute;
    bottom: 55px;
    z-index: 9;
    left: 0;
    right: 0;
    text-align: center;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpsliderthree__pagination {
        bottom: 15px;
    }
}

.tpsliderthree__pagination .swiper-pagination-bullet {
    width: 35px;
    height: 4px;
    display: inline-block;
    border-radius: 5px;
    background-color: var(--tp-common-white);
    margin: 0 5px;
    opacity: 1;
}

.tpsliderthree__pagination .swiper-pagination-bullet-active {
    background: var(--tp-text-body);
}

.slider-bg-overlay {
    position: relative;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.slider-bg-overlay::before {
    position: absolute;
    content: "";
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: #040404;
    opacity: 0.9;
}

.slider-3 {
    height: 570px;
    display: flex;
    align-items: center;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .slider-3 {
        height: 490px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .slider-3 {
        height: 460px;
    }
}

@media (max-width: 767px) {
    .slider-3 {
        height: 400px;
    }
}

.slider-content-3 {
    margin-top: -10px;
    margin-left: 50px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .slider-content-3 {
        margin-left: 0;
    }
}

@media (max-width: 767px) {
    .slider-content-3 {
        margin-left: 0;
        padding-top: 0px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .slider-content-3 {
        margin-left: 0;
        padding-top: 0;
    }
}

.tpslider-item-5 .tp-slide-item__sub-title {
    margin-bottom: 15px;
}

.slider-bg-four {
    display: flex;
    align-items: center;
    height: 570px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .slider-bg-four {
        height: 500px;
        justify-content: center;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .slider-bg-four {
        padding-top: 120px;
        height: 400px;
        justify-content: center;
    }
}

@media (max-width: 767px) {
    .slider-bg-four {
        padding-top: 120px;
        height: 400px;
        justify-content: center;
    }
}

/*----------------------------------------*/
/*   05. CATEGORY CSS START
/*----------------------------------------*/
.tpcategory {
    position: relative;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .tpcategory {
        width: 25%;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpcategory {
        width: 33.3333%;
    }
}

@media (max-width: 767px) {
    .tpcategory {
        width: 50%;
    }
}

.tpcategory::after {
    position: absolute;
    content: "";
    top: 0;
    bottom: 0;
    margin: auto;
    text-align: center;
    right: -65px;
    height: 200px;
    width: 1px;
    background-color: var(--tp-border-3);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpcategory::after {
        right: -30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .tpcategory::after {
        display: none;
    }
}

.tpcategory:last-child::after {
    display: none;
}

.tpcategory:hover .tpcategory__icon {
    box-shadow: inset 0 0 0 60px var(--tp-theme-3);
}

.tpcategory:hover .tpcategory__icon svg {
    opacity: 1;
}

.tpcategory:hover .tpcategory__icon span {
    background-color: var(--tp-text-primary);
    position: absolute;
    color: var(--tp-common-white);
}

.tpcategory__icon {
    height: 130px;
    width: 130px;
    line-height: 130px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(247, 247, 247, 0.5);
    margin: auto;
    margin-bottom: 25px;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    position: relative;
}

@media (max-width: 767px) {
    .tpcategory__icon {
        height: 110px;
        width: 110px;
        line-height: 110px;
    }
}

.tpcategory__icon svg {
    opacity: 0.5;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpcategory__icon span {
    display: block;
    height: 40px;
    width: 40px;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    font-weight: 600;
    color: var(--tp-text-primary);
    background-color: var(--tp-common-white);
    border-radius: 50%;
    position: absolute;
    top: 0;
    right: 0;
    opacity: 1;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.06);
}

.tpcategory__title {
    text-align: center;
    line-height: 1.2;
    font-size: 18px;
}

@media (max-width: 767px) {
    .tpcategory__title {
        font-size: 16px;
    }
}

.tpcategory__title a {
    background-image: linear-gradient(#969696, #969696), linear-gradient(#969696, #969696);
    display: inline;
    background-size: 0% 1px, 0 1px;
    background-position: 100% 100%, 0 83%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
    line-height: 1.4;
}

.tpcategory__title a:hover {
    background-size: 0 1px, 100% 1px;
}

.custom-row {
    display: flex;
    flex-wrap: wrap;
}

.category-border {
    border-bottom: 1px solid var(--tp-border-3);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .category-menu-off {
        display: none;
    }
}

.product-sidebar__product-item {
    margin-right: 30px;
}

.product-sidebar__product-category {
    margin-left: -10px;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .product-sidebar__product-category {
        margin-left: 0;
    }
}

.product-sidebar__title {
    font-weight: 600;
    font-size: 16px;
    color: var(--tp-text-body);
    padding-bottom: 17px;
    border-bottom: 1px solid #f1f1f1;
}

/* .product-sidebar__brand .form-check {
    margin-left: -20px;
    position: relative;
} */

.product-sidebar__brand .form-check::before {
    position: absolute;
    content: "";
}

.product-sidebar__brand .form-check a:hover {
    color: var(--tp-text-primary);
}

.product-sidebar .form-check:hover.form-check span i {
    color: var(--tp-text-body);
}

.product-sidebar .form-check span i {
    font-size: 14px;
    color: #e3e3e3;
}

.product-sidebar .form-check-label {
    font-size: 14px;
    color: var(--tp-text-secondary);
    margin-left: -8px;
    cursor: pointer;
}

.product-sidebar .form-check-input {
    height: 10px;
    width: 10px;
    background-color: #e3e3e3;
    transform: translateY(4px);
}

.product-sidebar .form-check-input[type=checkbox] {
    border-radius: 0;
    border: transparent;
}

.product-sidebar .form-check-input:checked {
    background-color: var(--tp-text-body);
}

.product-sidebar input:checked+label {
    color: var(--tp-text-body);
}

.product-sidebar .form-check-input:focus {
    box-shadow: none;
}

.product-sidebar .ui-widget.ui-widget-content {
    border: 0px solid #ece8fe;
    height: 2px;
    background: #ece8fe;
}

.product-sidebar .ui-slider-horizontal .ui-slider-range {
    background: var(--tp-text-body);
}

.product-sidebar .ui-slider-horizontal .ui-slider-handle {
    top: -4px;
}

.product-sidebar .ui-slider .ui-slider-handle {
    width: 10px;
    height: 10px;
    cursor: pointer;
    -ms-touch-action: none;
    touch-action: none;
    border: none;
    background-color: var(--tp-text-body);
    outline: 0;
}

.product-sidebar .ui-corner-all,
.product-sidebar .ui-corner-bottom,
.product-sidebar .ui-corner-right,
.product-sidebar .ui-corner-br {
    border-radius: 0;
}

.product-sidebar .price-filter input {
    border: none;
    font-size: 14px;
    margin-left: -11px;
}

.productsidebar {
    margin-left: 10px;
}

.product-sidebar .form-check-input.black-input {
    background-color: var(--tp-text-body);
}

.product-sidebar .form-check-input.blue-input {
    background-color: #0084ff;
}

.product-sidebar .form-check-input.grey-input {
    background-color: #d4d4d4;
}

.product-sidebar .form-check-input.green-input {
    background-color: #61b163;
}

.product-sidebar .form-check-input.red-input {
    background-color: #ff6262;
}

.product-sidebar .form-check-input.yellow-input {
    background-color: #ffc000;
}

/*----------------------------------------*/
/*  06. PRODUCT CSS START
/*----------------------------------------*/
.tpproduct:hover .tpproduct__priceinfo-list {
    bottom: 0px;
    visibility: hidden;
    opacity: 0;
}

.tpproduct:hover .tpproduct__cart {
    visibility: visible;
    opacity: 1;
    bottom: -20px;
}

.tpproduct:hover .product-thumb-secondary {
    opacity: 1;
}

.tpproduct:hover .product-thumb-secondary img {
    transform: scale3d(1.1, 1.1, 1.1) translateZ(0);
}

.tpproduct__thumb {
    position: relative;
    margin-bottom: 17px;
}

.tpproduct__thumb:hover .tpproduct__thumb-action a {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    filter: alpha(opacity=100);
}

.tpproduct__thumb-discount {
    position: absolute;
    top: 25px;
    left: 25px;
    padding: 4px 7px;
    background-color: var(--tp-text-primary);
    font-weight: 600;
    font-size: 12px;
    color: var(--tp-common-white);
    line-height: 1;
    border-radius: 5px;
    animation: firefly 2s linear infinite;
    z-index: 2;
}

.tpproduct__thumb-topsall {
    position: absolute;
    top: 25px;
    left: 25px;
    padding: 4px 7px;
    background-color: var(--tp-text-primary);
    font-weight: 600;
    font-size: 12px;
    color: var(--tp-common-white);
    line-height: 1;
    border-radius: 5px;
    animation: firefly 2s linear infinite;
    z-index: 2;
}

.tpproduct__thumb-volt {
    position: absolute;
    top: 25px;
    left: 25px;
    padding: 4px 7px;
    background-color: var(--tp-theme-5);
    font-weight: 600;
    font-size: 12px;
    color: var(--tp-common-white);
    line-height: 1;
    border-radius: 5px;
    animation: firefly 2s linear infinite;
    z-index: 2;
}

.tpproduct__thumb-action {
    position: absolute;
    right: 0;
    left: 0;
    margin: auto;
    text-align: center;
    top: 50%;
    transform: translateY(-50%);
}

.tpproduct__thumb-action a {
    font-size: 16px;
    color: var(--tp-text-body);
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;
    background-color: var(--tp-common-white);
    border-radius: 6px;
    display: inline-block;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
    opacity: 0;
    visibility: hidden;
    -webkit-transform: translate(0, 20px);
    -ms-transform: translate(0, 20px);
    -o-transform: translate(0, 20px);
    transform: translate(0, 20px);
    -webkit-box-shadow: 0px 4px 11px 0px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0px 4px 11px 0px rgba(0, 0, 0, 0.3);
    margin: 0 3px;
}

.tpproduct__thumb-action a:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

.tpproduct__thumb-action a.comphare {
    -webkit-transition: transform 0.3s ease 0s, background-color 0.3s ease 0s, opacity 0.3s ease 0s;
    -o-transition: transform 0.3s ease 0s, background-color 0.3s ease 0s, opacity 0.3s ease 0s;
    transition: transform 0.3s ease 0s, background-color 0.3s ease 0s, opacity 0.3s ease 0s;
}

.tpproduct__thumb-action a.quckview {
    -webkit-transition: transform 0.4s ease 0s, background-color 0.4s ease 0s, opacity 0.4s ease 0s;
    -o-transition: transform 0.4s ease 0s, background-color 0.4s ease 0s, opacity 0.4s ease 0s;
    transition: transform 0.4s ease 0s, background-color 0.4s ease 0s, opacity 0.4s ease 0s;
}

.tpproduct__thumb-action a.wishlist {
    -webkit-transition: transform 0.6s ease 0s, background-color 0.6s ease 0s, opacity 0.6s ease 0s;
    -o-transition: transform 0.6s ease 0s, background-color 0.6s ease 0s, opacity 0.6s ease 0s;
    transition: transform 0.6s ease 0s, background-color 0.6s ease 0s, opacity 0.6s ease 0s;
    line-height: 46px;
}

.tpproduct__thumb-bg {
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
    top: 50%;
    transform: translateY(-50%);
}

.tpproduct__thumb-bg a {
    margin: 0 10px;
    font-size: 16px;
    padding: 14px 0;
    display: inline-block;
    line-height: 1;
    color: #999999;
}

.tpproduct__thumb-bg a:hover {
    color: var(--tp-text-body);
}

.tpproduct__thumb img {
    border-radius: 6px;
    width: 100%;
}

.tpproduct__thumb-icon {
    position: relative;
    z-index: 2;
}

.tpproduct__rating ul li {
    display: inline-block;
    list-style: none;
}

.tpproduct__rating ul li a {
    font-size: 10px;
    color: var(--tp-text-primary);
}

.tpproduct__rating ul li span {
    font-size: 10px;
}

.tpproduct__ratingicon a {
    font-size: 14px;
    color: var(--tp-text-primary);
}

.tpproduct__ratingicon .max-star {
    color: #e0e0e0;
}

.tpproduct__ratingarea {
    position: absolute;
    left: 0;
    right: 0;
    transition: all 0.2s cubic-bezier(0.28, 0.12, 0.22, 1);
    will-change: opacity, transform;
    z-index: 2;
    opacity: 0;
    visibility: hidden;
    padding-bottom: 18px;
    transform: translateY(8px);
}

.tpproduct__content-area {
    z-index: 2;
    position: relative;
    transform: translateY(5px);
    transition: 0.2s;
    margin-bottom: 10px;
}

.tpproduct__thumbitem {
    position: relative;
    z-index: 2;
}

.tpproduct__thumbitem:hover .thumbitem-secondary {
    opacity: 1;
}

.tpproduct__thumbitem:hover .thumbitem-secondary img {
    transform: scale3d(1.1, 1.1, 1.1) translateZ(0);
}

.tpproduct__thumbitem:hover .tpproductactionbg {
    transform: translate(0, 0);
    opacity: 1;
    visibility: visible;
}

.tpproduct__title {
    font-size: 15px;
    font-weight: 400;
    color: var(--tp-text-secondary);
    margin-bottom: 12px;
}

.tpproduct__title a {
    display: inline-block;
}

.tpproduct__title a:hover {
    color: var(--tp-text-primary);
}

.tpproduct__priceinfo-list {
    position: absolute;
    bottom: -20px;
    left: 0;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpproduct__priceinfo-list span {
    font-weight: 600;
    font-size: 16px;
}

.tpproduct__priceinfo-list-oldprice {
    color: var(--tp-text-tertiary);
    text-decoration: line-through;
    margin-left: 8px;
}

.tpproduct__priceinfo-oldprice {
    text-decoration: line-through;
}

.tpproduct__ammount {
    font-size: 16px;
    font-weight: 600;
}

.tpproduct__cart {
    position: absolute;
    left: 0;
    bottom: -40px;
    left: 0;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpproduct__cart i {
    color: var(--tp-text-primary);
    margin-right: 5px;
}

.tpproduct__cart a {
    font-size: 16px;
    font-weight: 600;
    text-decoration: underline;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpproduct__cart a:hover {
    color: var(--tp-text-primary);
}

.tpproduct__variation {
    position: absolute;
    z-index: 2;
    background: #fff;
    bottom: -8px;
    left: 0;
    right: 0;
    padding-top: 12px;
}

.tpproduct__variation a:hover .tpproduct__termshape-bg {
    transform: scale(0.7);
}

.tpproduct__variation a:hover .tpproduct__termshape-border {
    opacity: 1;
    transform: scale(1.4);
}

.tpproduct__variationitem {
    display: inline-block;
}

.tpproduct__termshape {
    position: relative;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 3px;
}

.tpproduct__termshape-bg {
    transition: all 0.25s ease;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #99c4ff;
}

.tpproduct__termshape-border {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 1px solid #99c4ff;
    opacity: 0;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpproductactionbg {
    background-color: var(--tp-common-white);
    display: inline-block;
    padding: 0 8px;
    border-radius: 6px;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
    transform: translate(0, 20px);
    opacity: 0;
    visibility: hidden;
    transition: transform 0.5s ease 0s, background-color 0.5s ease 0s, opacity 0.4s ease 0s;
}

.tpproductdot a:hover .tpproductdot__termshape-bg {
    transform: scale(0.7);
}

.tpproductdot a:hover .tpproductdot__termshape-border {
    opacity: 1;
    transform: scale(1.8);
}

.tpproductdot__variationitem {
    left: 0;
    right: 0;
    padding-top: 12px;
}

.tpproductdot__variationitem {
    display: inline-block;
}

.tpproductdot__termshape {
    position: relative;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 3px;
}

.tpproductdot__termshape-bg {
    transition: all 0.25s ease;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #99c4ff;
}

.tpproductdot__termshape-border {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 1px solid #99c4ff;
    opacity: 0;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.red-product-bg {
    background-color: #d51243;
}

.red-product-border {
    border: 1px solid #d51243;
}

.purple-product-bg {
    background-color: #be9aff;
}

.purple-product-border {
    border: 1px solid #be9aff;
}

.orange-product-bg {
    background-color: #ff7373;
}

.orange-product-border {
    border: 1px solid #ff7373;
}

.tpproductitem {
    z-index: 9;
    background-color: #fff;
    padding-bottom: 10px;
}
.tpproductitem:hover {
    z-index: 10;
}
.tpproductitem:hover .product-content-fade {
    opacity: 1;
}

.tpproductitem:hover .tpproduct__ratingarea {
    opacity: 1;
    visibility: visible;
    transform: translateY(0px);
}

.tpproductitem:hover:after {
    opacity: 1;
}

.tpproductitem:after {
    position: absolute;
    top: -20px;
    bottom: -10px;
    left: -20px;
    right: -20px;
    background-color: #fff;
    border-radius: 5px;
    opacity: 0;
    -webkit-transition: opacity 0.2s cubic-bezier(0.28, 0.12, 0.22, 1);
    transition: opacity 0.2s cubic-bezier(0.28, 0.12, 0.22, 1);
    will-change: opacity;
    box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.06);
    pointer-events: none;
    z-index: 1;
    margin-bottom: -33px;
    content: "";
}

.red-product-bg {
    background-color: #d51243;
}

.red-product-border {
    border: 1px solid #d51243;
}

.yellow-product-bg {
    background-color: #ffd8a6;
}

.yellow-product-border {
    border: 1px solid #ffd8a6;
}

.green-product-bg {
    background-color: #c2ddce;
}

.green-product-border {
    border: 1px solid #c2ddce;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .tpdealproduct__thumb img {
        max-width: 100%;
    }
}

.tpdealproduct__offer-price {
    position: absolute;
    top: 75px;
    right: 155px;
    font-size: 24px;
    font-weight: 600;
    color: var(--tp-common-white);
    background-color: var(--tp-text-primary);
    border-radius: 50%;
    padding: 19px 18px;
    animation: hotdeal 2s linear infinite;
}

.tpdealproduct__offer-price span {
    font-size: 12px;
    display: block;
    color: var(--tp-common-white);
}

.tpdealcontact {
    margin-left: -50px;
    padding-right: 179px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px),
only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpdealcontact {
        padding-right: 50px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpdealcontact {
        margin-left: 0px;
        padding-right: 0px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpdealcontact {
        margin-left: 0;
        padding-right: 20px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpdealcontact {
        margin-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 767px) {
    .tpdealcontact {
        margin-left: 15px;
        padding-right: 15px;
    }
}

.tpdealcontact__price span {
    font-size: 20px;
    font-weight: 600;
    color: var(--tp-text-primary);
}

@media (max-width: 767px) {
    .tpdealcontact__price span {
        font-size: 16px;
    }
}

.tpdealcontact__price del {
    font-size: 20px;
    font-weight: 600;
    color: var(--tp-text-tertiary);
}

@media (max-width: 767px) {
    .tpdealcontact__price del {
        font-size: 16px;
    }
}

.tpdealcontact__title {
    font-size: 50px;
    color: var(--tp-text-body);
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -1.5px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpdealcontact__title {
        font-size: 42px;
    }
}

@media (max-width: 767px) {
    .tpdealcontact__title {
        font-size: 23px;
        letter-spacing: 0;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpdealcontact__title {
        font-size: 24px;
        letter-spacing: 0;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpdealcontact__title {
        font-size: 32px;
    }
}

.tpdealcontact__text p {
    color: var(--tp-text-secondary);
    font-size: 18px;
    line-height: 1.66;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpdealcontact__text p {
        width: 520px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpdealcontact__text p {
        font-size: 16px;
        width: 410px;
        line-height: 1.5;
    }
}

@media (max-width: 767px) {
    .tpdealcontact__text p {
        font-size: 16px;
        width: 100%;
        line-height: 1.4;
    }
}

.tpdealcontact__progress .progress {
    height: 6px;
    border-radius: 5px;
    background-color: var(--tp-common-white);
}

.tpdealcontact__progress .progress-bar {
    color: var(--tp-common-white);
    text-align: center;
    white-space: nowrap;
    background-color: var(--tp-text-primary);
    transition: width 0.6s ease;
    border-radius: 5px;
}

.tpdealcontact__countdown span {
    display: inline-block;
}

.tpdealcontact__countdown span.cdown {
    background-color: var(--tp-common-white);
    height: 92px;
    width: 80px;
    text-align: center;
    line-height: 65px;
    color: var(--tp-text-body);
    font-size: 36px;
    font-weight: 700;
    border-radius: 6px;
    margin-right: 15px;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
}

@media (max-width: 767px) {
    .tpdealcontact__countdown span.cdown {
        margin-bottom: 10px;
    }
}

.tpdealcontact__countdown span.cdown p {
    margin-top: -15px;
    font-size: 16px;
    color: var(--tp-text-secondary);
}

.tpdealcontact__count {
    display: flex;
    align-items: center;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
(max-width: 767px) {
    .tpdealcontact__count {
        display: inherit;
    }
}

.tpdealcontact__count i {
    font-style: normal;
    font-size: 16px;
    margin-bottom: 0;
    padding-left: 13px;
    color: var(--tp-text-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
(max-width: 767px) {
    .tpdealcontact__count i {
        padding-top: 10px;
        padding-left: 0px;
        display: block;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
(max-width: 767px) {
    .tpdealcontact__count i br {
        display: none;
    }
}

.swiper-container.product-active {
    margin-right: -500px;
}

.discount-product {
    z-index: 1;
}

.discount-product .tpdealcontact {
    margin-left: -13px;
    padding-right: 140px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .discount-product .tpdealcontact {
        padding-right: 40px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .discount-product .tpdealcontact {
        margin-left: 0;
        padding-right: 10px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .discount-product .tpdealcontact {
        margin-left: 0;
        padding-right: 10px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .discount-product .tpdealcontact {
        margin-left: 15px;
        padding-right: 15px;
    }
}

.tpdealproduct {
    margin-left: 68px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpdealproduct {
        margin-left: 10px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .tpdealproduct {
        margin-left: 0px;
        margin-bottom: 30px;
    }
}

.tp-fourth-deal .tpdealcontact {
    margin-left: 0px;
    padding-right: 0px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-fourth-deal .tpdealcontact {
        padding-right: 10px;
    }
}

.video-icon {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
}

.platinamdell .tpdealcontact {
    margin-left: 0;
}

.tpdealslide {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.tpdealslide::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--tp-theme-13);
}

.tp-red-deal-text {
    padding-right: 0;
}

.tpvideo-bg {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 50%;
    background-repeat: no-repeat;
    background-size: cover;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpvideo-bg {
        position: relative;
        height: 450px;
        width: 100%;
    }
}

@media (max-width: 767px) {
    .tpvideo-bg {
        position: relative;
        height: 450px;
        width: 100%;
    }
}

.tpvideo-bg a {
    height: 120px;
    width: 120px;
    line-height: 120px;
    text-align: center;
    background-color: var(--tp-common-white);
    border-radius: 50%;
    color: var(--tp-theme-primary);
    display: block;
    font-size: 20px;
    animation: tp-pulse-2 1s infinite;
}

@media (max-width: 767px) {
    .tpvideo-bg a {
        height: 80px;
        width: 80px;
        line-height: 80px;
    }
}

.tpvideo-bg a:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

@media (max-width: 767px) {
    .video-area {
        padding-top: 0 !important;
    }
}

.product-thumb-secondary,
.thumbitem-secondary {
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    border-radius: 6px;
}

.product-thumb-secondary img,
.thumbitem-secondary img {
    transition: opacity 0.8s ease, transform 1.1s cubic-bezier(0.15, 0.75, 0.5, 1) 0s, -webkit-transform 1.1s cubic-bezier(0.15, 0.75, 0.5, 1) 0s;
}

.product-item-count span {
    color: #999;
    font-size: 18px;
}

.product-item-count span b {
    font-weight: 600;
    color: var(--tp-common-black);
}

.product-filter-nav {
    margin-left: 5px;
}

@media (max-width: 767px) {
    .product-filter-nav {
        transform: translateY(15px);
    }
}

.product-filter-nav.tpnavbar .nav-link {
    margin-left: 15px;
}

.tp-shop-selector .nice-select {
    border: transparent;
    color: var(--tp-text-body);
    font-weight: 600;
}

.tp-shop-selector .nice-select::after {
    right: 20px;
}

@media (max-width: 767px) {
    .tp-shop-selector .nice-select {
        padding-left: 0;
    }
}

@media (max-width: 767px) {
    .product-navtabs {
        justify-content: flex-start !important;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .product-navtabs {
        justify-content: flex-end !important;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .filter-product {
        margin-left: 0;
    }
}

.filter-product p {
    font-size: 16px;
}

/*----------------------------------------*/
/*  07. SHOP CSS START
/*----------------------------------------*/
.tpshopitem {
    height: 220px;
    width: 220px;
    object-fit: cover;
}

.tpshopitem img {
    width: 100%;
    border-radius: 6px;
}

@media (max-width: 767px) {
    .tpshopitem {
        text-align: center;
    }
}

.whiteproduct {
    background-color: var(--tp-common-white);
}

.whiteproduct__thumb img {
    max-width: 100%;
}

.whiteproduct__content {
    padding: 0 40px 40px 40px;
}

.whiteproduct__title {
    color: var(--tp-text-secondary);
    font-size: 15px;
    margin-bottom: 2px;
}

.whiteproduct__title a:hover {
    color: var(--tp-text-primary);
}

.whiteproduct__text span {
    font-weight: 600;
    font-size: 16px;
}

.whiteproduct__rating i {
    color: var(--tp-text-primary);
    font-size: 14px;
}

.whiteproduct__rating span {
    font-size: 14px;
}

.tpproductarrow {
    position: relative;
    z-index: 2;
    justify-content: flex-end;
}

@media (max-width: 767px) {
    .tpproductarrow {
        justify-content: flex-start;
        margin-bottom: 15px;
    }
}

.tpproductarrow__prv {
    font-size: 14px;
    font-weight: 600;
    background-color: #fff;
    padding: 6px 14px;
    line-height: 1;
    margin-right: 10px;
    border-radius: 6px;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpproductarrow__prv:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

.tpproductarrow__prv:hover i {
    color: var(--tp-common-white);
}

.tpproductarrow__prv i {
    margin-right: 6px;
}

.tpproductarrow__nxt {
    font-size: 14px;
    font-weight: 600;
    background-color: #fff;
    padding: 6px 14px;
    line-height: 1;
    border-radius: 6px;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpproductarrow__nxt:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

.tpproductarrow__nxt:hover i {
    color: var(--tp-common-white);
}

.tpproductarrow__nxt i {
    margin-left: 6px;
}

.tpsidebar li {
    list-style: none;
}

.tpsidebar__form-check .form-check-input {
    margin-top: 0;
    box-shadow: none;
}

.tpsidebar__form-check .form-check-input[type=checkbox] {
    border-radius: 0;
}

.tpsidebar__form-check label {
    font-size: 14px;
    color: var(--tp-text-secondary);
    margin-left: 10px;
}

.tpsidebar__form-check span {
    margin-left: auto;
}

.filter-product-title {
    font-size: 20px;
    margin-bottom: 10px;
    font-weight: 700;
}

@media (max-width: 767px) {
    .filter-product-title {
        font-size: 18px;
    }
}

.filter-product-title a:hover {
    color: var(--tp-text-primary);
}

.tpproduct__ammount del {
    font-weight: 500;
    color: #747691;
    margin-left: 10px;
}

.tpproduct__action a {
    font-size: 16px;
    color: var(--tp-text-body);
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;
    background-color: var(--tp-common-white);
    border-radius: 6px;
    display: inline-block;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
}

.tpproduct__action a:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

/*----------------------------------------*/
/*  08. BANNER CSS START
/*----------------------------------------*/
.banneritem__thumb:hover .banneritem__content i {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.banneritem__thumb img {
    width: 100%;
}

.banneritem__content {
    position: absolute;
    bottom: 26px;
    left: 0;
    right: 0;
    text-align: center;
    margin: 0 auto;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .banneritem__content {
        bottom: 5px;
    }
}

.banneritem__content i {
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
    border-radius: 50%;
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0;
    visibility: hidden;
    transform: scale(0);
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .banneritem__content i {
        height: 35px;
        width: 35px;
        line-height: 35px;
        font-size: 14px;
        margin-bottom: 0;
    }
}

.banneritem__content i:hover {
    background-color: var(--tp-common-white);
    color: var(--tp-text-primary);
}

.banneritem__content p {
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 2px;
    color: var(--tp-text-secondary);
}

.banneritem__content-tiele {
    font-size: 20px;
    font-weight: 700;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .banneritem__content-tiele {
        font-size: 14px;
    }
}

.banner-center {
    bottom: 51px;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .banner-center {
        bottom: 15px;
    }
}

.banner-center .banneritem__content-tiele {
    font-size: 40px;
    font-weight: 600;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .banner-center .banneritem__content-tiele {
        font-size: 20px;
    }
}

.tpexclusive__content {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 100px;
}

@media (max-width: 767px) {
    .tpexclusive__content {
        left: 15px;
    }
}

.tpexclusive__contentarea {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    text-align: center;
    transform: translateY(-50%);
}

.tpexclusive__contentarea p {
    color: var(--tp-common-white);
    font-size: 18px;
    line-height: 30px;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .tpexclusive__contentarea p {
        font-size: 14px;
        line-height: 20px;
    }
}

.tpexclusive__thumb img {
    border-radius: 6px;
}

.tpexclusive__subtitle {
    font-size: 20px;
    font-weight: 400;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpexclusive__subtitle {
        font-size: 15px;
    }
}

@media (max-width: 767px) {
    .tpexclusive__subtitle {
        font-size: 12px;
    }
}

.tpexclusive__title {
    font-size: 50px;
    font-weight: 600;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpexclusive__title {
        font-size: 20px;
    }
}

@media (max-width: 767px) {
    .tpexclusive__title {
        font-size: 16px;
        margin-bottom: 15px;
    }
}

.tpexclusive__btn .tp-btn {
    background-color: transparent;
    border: 1px solid #595a56;
    color: var(--tp-common-white);
}

@media (max-width: 767px) {
    .tpexclusive__btn .tp-btn {
        padding: 7px 10px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpexclusive__btn .tp-btn {
        padding: 10px 20px;
    }
}

.tpexclusive__btn .tp-btn:hover {
    background-color: var(--tp-text-primary);
    border: 1px solid var(--tp-text-primary);
}

.subcolor {
    color: var(--tp-text-primary);
}

.tpbanneritem {
    margin-left: 55px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .tpbanneritem {
        margin-left: 20px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
only screen and (min-width: 576px) and (max-width: 767px),
(max-width: 767px) {
    .tpbanneritem {
        margin-left: 0px;
    }
}

@media (max-width: 767px) {
    .tpbanneritem__thumb {
        margin-bottom: 10px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1600px),
only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
only screen and (min-width: 576px) and (max-width: 767px),
(max-width: 767px) {
    .tpbanneritem__thumb img {
        width: 100%;
    }
}

.tpbanneritem__text {
    position: absolute;
    top: 50px;
    left: 50px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpbanneritem__text {
        top: 30px;
        left: 30px;
    }
}

@media (max-width: 767px) {
    .tpbanneritem__text {
        top: 15px;
        left: 15px;
    }
}

.tpbanneritem__text-title {
    font-size: 20px;
    font-weight: 600;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpbanneritem__text-title {
        margin-bottom: 0px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpbanneritem__text-title {
        margin-bottom: 0px;
        font-size: 16px;
    }
}

.tpbanneritem__text-title a:hover {
    color: var(--tp-text-primary);
}

.tpbanneritem__text-price {
    color: var(--tp-text-primary);
    font-size: 14px;
    font-weight: 600;
}

.tpbanneritem__content {
    position: absolute;
    top: 40px;
    right: 10px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .tpbanneritem__content {
        top: 20px;
        right: 55px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpbanneritem__content {
        right: 55px;
    }
}

@media (max-width: 767px) {
    .tpbanneritem__content {
        right: 55px;
        top: 10px;
    }
}

.tpbanneritem__content p {
    font-size: 16px;
    margin-bottom: 0;
    color: var(--tp-text-secondary);
}

.tpbanneritem__title {
    font-size: 30px;
    color: var(--tp-text-body);
    font-weight: 600;
    line-height: 1.15;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpbanneritem__title {
        font-size: 20px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpbanneritem__title {
        font-size: 20px;
        margin-bottom: 20px;
    }
}

@media (max-width: 767px) {
    .tpbanneritem__title {
        margin-bottom: 5px;
        font-size: 16px;
    }
}

@media (max-width: 767px),
only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpbanneritem__title a br {
        display: none;
    }
}

.tpbanneritem__title a:hover {
    color: var(--tp-text-primary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpbanneritem__btn {
        right: auto;
        left: 10px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpbanneritem__btn .tp-btn {
        padding: 10px 10px;
    }
}

@media (max-width: 767px) {
    .tpbanneritem__btn .tp-btn {
        padding: 8px 10px;
        font-size: 12px;
    }
}

@media (max-width: 767px) {
    .tpbanneritem__btn .tp-btn i {
        margin-left: 2px;
    }
}

.tp-banner-item-small {
    position: absolute;
    bottom: 50px;
    left: 50px;
    font-size: 14px;
    font-weight: 600;
}

@media (max-width: 767px) {
    .tp-banner-item-small {
        left: 15px;
    }
}

.banneritem img {
    width: 100%;
}

.banneroverlay {
    position: relative;
    z-index: 1;
}

.banneroverlay::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    background-color: var(--tp-common-black);
    opacity: 0.5;
}

.bannertext {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 9;
    transform: translateY(-50%);
}

.bannertext__subtitle {
    font-size: 16px;
    color: var(--tp-common-white);
}

.bannertext__title {
    font-size: 40px;
    color: var(--tp-common-white);
    line-height: 1.25;
    font-weight: 600;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .bannertext__title {
        font-size: 34px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .bannertext__title {
        font-size: 28px;
    }
}

.bannertext__title a:hover {
    color: var(--tp-text-primary);
}

.exclusiveitem__thumb img {
    border-radius: 6px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px),
only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .exclusiveitem__thumb img {
        max-width: 100%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1600px),
only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .exclusivearea__thumb img {
        max-width: 100%;
    }
}

.bannerbox {
    position: absolute;
    top: 55px;
    left: 60px;
}

@media only screen and (min-width: 1601px) and (max-width: 1700px) {
    .bannerbox {
        top: 30px;
        left: 50px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .bannerbox {
        top: 30px;
        left: 42px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .bannerbox {
        top: 25px;
        left: 35px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .bannerbox {
        top: 15px;
        left: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .bannerbox {
        top: 10px;
        left: 10px;
    }
}

.bannerbox__subtitle {
    font-size: 16px;
    color: #999999;
    display: block;
}

.bannerbox__subtitle a {
    color: var(--tp-text-primary);
    text-decoration: underline;
    font-weight: 700;
}

.bannerbox__title {
    font-size: 30px;
    color: var(--tp-text-body);
    font-weight: 600;
    line-height: 1.34;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .bannerbox__title br {
        display: none;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .bannerbox__title {
        margin-bottom: 50px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .bannerbox__title {
        font-size: 24px;
        margin-bottom: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .bannerbox__title {
        font-size: 18px;
        margin-bottom: 10px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .bannerbox__title {
        font-size: 15px;
        margin-bottom: 5px;
    }
}

.bannerbox__title a:hover {
    color: var(--tp-text-primary);
}

.bannerbox__btn .tp-btn {
    padding: 10px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .bannerbox__btn .tp-btn i {
        margin-left: 2px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .banner-bottom-bg {
        margin-bottom: 50px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .banner-bottom-bg {
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .banner-bottom-bg {
        margin-bottom: 10px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .banner-bottom-bg {
        margin-bottom: 5px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1600px),
only screen and (min-width: 1200px) and (max-width: 1399px) {
    .banner-right-bg {
        margin-bottom: 50px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .banner-right-bg {
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .banner-right-bg {
        margin-bottom: 15px;
    }
}

.banner-bottom-title {
    margin-bottom: 0;
    line-height: 1;
}

.banner-bottom-title a:hover {
    color: var(--tp-text-primary);
}

.banner-shape-primary {
    position: absolute;
    right: 0;
    top: 25px;
}

@media only screen and (min-width: 1601px) and (max-width: 1700px) {
    .banner-shape-primary {
        right: -20px;
    }
}

.banner-shape-secondary {
    position: absolute;
    left: 535px;
    bottom: 45px;
}

@media (max-width: 767px) {
    .banner-shape-secondary {
        left: 50px;
    }
}

.white-banner .tpslider-banner__sub-title {
    color: #ff9fb7;
}

.white-banner .tpslider-banner__title {
    color: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpbnner-height-5 {
        margin-bottom: 38px;
    }
}

@media (max-width: 767px) {
    .tpbnner-height-5 {
        margin-bottom: 20px;
    }
}

.tpbannerthumb-5 {
    margin-left: 0;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-slider-sm-banner {
        margin-bottom: 17px;
    }
}

/*----------------------------------------*/
/*  09. MARQUE CSS START
/*----------------------------------------*/
.mp-marque-slider {
    position: absolute;
    top: 95px;
    width: 100%;
}

@media (max-width: 767px) {
    .mp-marque-slider {
        top: 45px;
    }
}

.mp-marque-slider p {
    text-transform: uppercase;
    font-size: 100px;
    font-weight: 700;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 1601px) and (max-width: 1700px) {
    .mp-marque-slider p {
        font-size: 85px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .mp-marque-slider p {
        font-size: 75px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .mp-marque-slider p {
        font-size: 64px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .mp-marque-slider p {
        font-size: 53px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .mp-marque-slider p {
        font-size: 40px;
    }
}

@media (max-width: 767px) {
    .mp-marque-slider p {
        font-size: 16px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .mp-marque-slider p {
        font-size: 30px;
    }
}

.mp-marque-slider p span {
    background-clip: text;
    color: transparent;
    background-image: url("../img/blog/blog-thumb-07.jpg");
    background-size: cover;
    -webkit-background-clip: text;
}

@media (min-width: 1801px) and (max-width: 1880px) {
    .mp-marque-slider p {
        font-size: 96px;
    }
}

.mp-marque-slider .swiper-wrapper {
    -webkit-transition-timing-function: linear !important;
    transition-timing-function: linear !important;
}

.swiper--top.swiper-container {
    overflow: visible;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .platinamdell {
        padding-top: 100px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .platinamdell {
        padding-top: 80px;
    }
}

@media (max-width: 767px) {
    .platinamdell {
        padding-top: 0px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .platinamdell {
        padding-top: 50px;
    }
}

/*----------------------------------------*/
/*  00. 10. PORTFOLIO CSS START
/*----------------------------------------*/
/*----------------------------------------*/
/*  11. BLOG CSS START
/*----------------------------------------*/
.blog-btn {
    font-size: 16px;
    font-weight: 600;
}

@media (max-width: 767px) {
    .blog-btn {
        font-size: 14px;
    }
}

.blog-btn:hover {
    color: var(--tp-text-primary);
}

.blog-btn:hover i {
    animation: iconarrow 0.4s linear;
}

.blogitem:hover .blogitem__thumb img {
    transform: scale(1.1);
}

.blogitem__thumb {
    border-radius: 6px;
}

.blogitem__thumb img {
    -webkit-transition: all 1s ease-out 0s;
    -moz-transition: all 1s ease-out 0s;
    -ms-transition: all 1s ease-out 0s;
    -o-transition: all 1s ease-out 0s;
    transition: all 1s ease-out 0s;
    width: 100%;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px) {
    .blogitem__thumb img {
        max-width: 100%;
    }
}

.blogitem__content ul li {
    list-style: none;
    display: inline-block;
    margin-right: 35px;
    position: relative;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .blogitem__content ul li {
        margin-right: 15px;
    }
}

.blogitem__content ul li::before {
    position: absolute;
    content: "";
    top: 50%;
    right: -19px;
    height: 15px;
    width: 2px;
    background: #ececec;
    transform: translateY(-50%);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .blogitem__content ul li::before {
        right: -10px;
    }
}

.blogitem__content ul li:last-child::before {
    display: none;
}

.blogitem__content ul li a {
    font-size: 14px;
    font-weight: 400;
    color: var(--tp-text-body);
}

.blogitem__content ul li a:hover {
    color: var(--tp-text-primary);
}

.blogitem__content ul li .date-color {
    color: var(--tp-text-secondary);
}

.blogitem__title {
    font-size: 20px;
    font-weight: 600;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
(max-width: 767px) {
    .blogitem__title {
        font-size: 16px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .blogitem__title {
        font-size: 15px;
    }
}

.blogitem__title a:hover {
    color: var(--tp-text-primary);
}

.blogitem__btn a {
    font-size: 14px;
    font-weight: 600;
    color: #cbcbcb;
    background-image: linear-gradient(#d51243, #d51243), linear-gradient(#d51243, #d51243);
    display: inline;
    background-size: 0% 1px, 0 1px;
    background-position: 100% 100%, 0 90%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
    line-height: 1.4;
}

.blogitem__btn a:hover {
    background-size: 0 1px, 100% 1px;
    color: var(--tp-text-primary);
}

.tpblogborder {
    border-right: 1px solid var(--tp-theme-7);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpblogborder {
        border: none;
    }
}

.blog-main-box {
    margin-right: -10px;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .blog-main-box {
        margin-right: 0;
    }
}

.postbox__thumb img {
    border-radius: 10px;
    max-width: 100%;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .postbox__wrapper {
        padding-right: 0;
    }
}

.postbox__meta span {
    margin-right: 55px;
    color: #5b5757;
    font-weight: 600;
    font-size: 14px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .postbox__meta span {
        margin-right: 18px;
    }
}

@media (max-width: 767px) {
    .postbox__meta span {
        margin-right: 15px;
        font-size: 12px;
    }
}

.postbox__meta span i {
    margin-right: 5px;
    color: var(--tp-text-primary);
}

.postbox__meta span a {
    text-transform: uppercase;
    display: inline-block;
    color: #5b5757;
    font-weight: 600;
}

.postbox__meta span a:hover {
    color: var(--tp-text-primary);
}

.postbox__title {
    font-weight: 700;
    font-size: 36px;
    line-height: 1.25;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .postbox__title {
        font-size: 30px;
    }
}

@media (max-width: 767px) {
    .postbox__title {
        font-size: 24px;
    }
}

.postbox__title a:hover {
    color: var(--tp-text-primary);
}

.postbox__text p {
    font-size: 16px;
}

@media (max-width: 767px) {
    .postbox__text p {
        font-size: 14px;
    }
}

.postbox .play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 88px;
    width: 88px;
    background: var(--tp-common-white);
    text-align: center;
    line-height: 88px;
    border-radius: 50%;
    color: var(--tp-text-primary);
    animation: tp-pulse-2 1.5s infinite;
}

.postbox .play-btn:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

.postbox__meta-img img {
    width: 100%;
    border-radius: 10px;
}

.postbox__tag span {
    font-size: 20px;
    margin-right: 15px;
    font-weight: 700;
}

.postbox__social-tag span {
    font-size: 20px;
    font-weight: 700;
    margin-right: 15px;
}

.postbox__social-tag a {
    margin-right: 30px;
    font-size: 18px;
}

.postbox__social-tag a:last-child {
    margin-right: 0;
}

.postbox__tag-border {
    border-top: 1px solid var(--tp-border-1);
    border-bottom: 1px solid var(--tp-border-1);
    padding: 30px 0;
}

.postbox__social-tag {
    text-align: end;
}

.postbox__social-tag a:hover {
    color: var(--tp-text-primary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .postbox__social-tag {
        margin-top: 15px;
        text-align: start;
    }
}

.postbox__content-area h4 {
    font-weight: 700;
    font-size: 36px;
    line-height: 1.25;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .postbox__content-area h4 {
        font-size: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .postbox__content-area h4 {
        font-size: 26px;
    }
}

@media (max-width: 767px) {
    .postbox__content-area h4 {
        font-size: 28px;
    }
}

.postbox__content-area p {
    font-size: 16px;
    margin-bottom: 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .postbox__content-area p {
        font-size: 15px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .postbox__content-area p br {
        display: none;
    }
}

.postbox__text-list ul li {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
    list-style: none;
}

.postbox__text-list ul li:last-child {
    margin-bottom: 0;
}

.postbox__text-list ul li i {
    margin-right: 20px;
    font-size: 18px;
    color: var(--tp-text-primary);
}

.postbox__comment {
    border-bottom: 1px solid var(--tp-border-1);
}

.postbox__comment ul li {
    margin-bottom: 10px;
    list-style: none;
}

.postbox__comment ul li.children {
    margin-left: 100px;
    margin-bottom: 30px;
}

@media (max-width: 767px) {
    .postbox__comment ul li.children {
        margin-left: 0px;
    }
}

.postbox__comment-form {
    margin-bottom: 20px;
}

.postbox__comment-form-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
}

.postbox__comment-form p {
    color: var(--tp-text-secondary);
    font-size: 16px;
    margin-bottom: 60px;
}

.postbox__comment-input {
    position: relative;
    margin-bottom: 30px;
    color: var(--tp-text-body);
}

.postbox__comment-input span {
    font-weight: 600;
    color: var(--tp-common-black);
    margin-bottom: 12px;
    display: block;
}

.postbox__comment-input input,
.postbox__comment-input textarea {
    height: 60px;
    padding: 0 20px;
    width: 100%;
    font-size: 14px;
    color: var(--tp-text-body);
    outline: none;
    border: 1px solid var(--tp-border-1);
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    -o-border-radius: 7px;
    -ms-border-radius: 7px;
    border-radius: 7px;
    box-shadow: 0px 16px 24px rgba(189, 196, 205, 0.13);
}

.postbox__comment-input input:focus,
.postbox__comment-input textarea:focus {
    border: 1px solid var(--tp-text-primary);
}

.postbox__comment-input input::placeholder,
.postbox__comment-input textarea::placeholder {
    color: var(--tp-text-secondary);
}

.postbox__comment-input input::placeholder:focus,
.postbox__comment-input textarea::placeholder:focus {
    color: var(--tp-text-primary);
}

.postbox__comment-input textarea {
    height: 175px;
    resize: none;
    padding-top: 20px;
    padding-bottom: 20px;
}

.postbox__comment-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 35px;
}

.postbox__comment-box {
    margin-bottom: 70px;
}

.postbox__comment-avater img {
    width: 80px;
    height: 80px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}

.postbox__comment-name {
    margin-bottom: 15px;
}

.postbox__comment-name h5 {
    font-size: 16px;
    line-height: 1;
    margin-bottom: 8px;
    font-weight: 500;
}

.postbox__comment-name span {
    font-size: 14px;
    color: var(--tp-text-secondary);
}

@media (max-width: 767px) {
    .postbox__comment-text {
        margin-left: 0;
        margin-top: 15px;
    }
}

.postbox__comment-text p {
    font-size: 16px;
    color: var(--tp-text-secondary);
    margin-bottom: 15px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .postbox__comment-text p {
        font-size: 15px;
    }
}

@media (max-width: 767px) {
    .postbox__comment-text p {
        font-size: 14px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .postbox__comment-text p br {
        display: none;
    }
}

.postbox__comment-reply {
    margin-top: 10px;
}

.postbox__comment-reply a {
    display: inline-block;
    color: var(--tp-text-primary);
    background: var(--tp-common-white);
    line-height: 22px;
    padding: 4px 10px;
    font-weight: 500;
    font-size: 14px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;
    -webkit-transition: all 0.2s ease-out 0s;
    -moz-transition: all 0.2s ease-out 0s;
    -ms-transition: all 0.2s ease-out 0s;
    -o-transition: all 0.2s ease-out 0s;
    transition: all 0.2s ease-out 0s;
}

.postbox__comment-reply a:hover {
    color: var(--tp-common-white);
    background: var(--tp-text-primary);
}

.postbox__comment-agree {
    padding-left: 5px;
}

.postbox__comment-agree input {
    margin: 0;
    appearance: none;
    -moz-appearance: none;
    display: block;
    width: 14px;
    height: 14px;
    background: var(--tp-common-white);
    border: 1px solid #b9bac1;
    outline: none;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;
    flex: 0 0 auto;
    -webkit-transform: translateY(-1px);
    -moz-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    -o-transform: translateY(-1px);
    transform: translateY(-1px);
}

.postbox__comment-agree input:checked {
    position: relative;
    background-color: var(--tp-theme-1);
    border-color: transparent;
}

.postbox__comment-agree input:checked::after {
    box-sizing: border-box;
    content: "\f00c";
    position: absolute;
    font-family: var(--tp-ff-fontawesome);
    font-size: 10px;
    color: var(--tp-common-white);
    top: 46%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.postbox__comment-agree input:hover {
    cursor: pointer;
}

.postbox__comment-agree label {
    padding-left: 8px;
    color: var(--tp-text-1);
    line-height: 1;
}

.postbox__comment-agree label a {
    color: var(--tp-common-black);
    font-weight: 600;
    padding-left: 4px;
}

.postbox__comment-agree label a:hover {
    color: var(--tp-theme-1);
}

.postbox__comment-agree label:hover {
    cursor: pointer;
}

.postbox-nav button {
    position: absolute;
    left: 0px;
    top: 50%;
    z-index: 1;
    transform: translateY(-50%);
}

.postbox-nav button.postbox-slider-button-next {
    left: auto;
    right: 0px;
}

.postbox-nav button i {
    height: 60px;
    width: 60px;
    line-height: 60px;
    background-color: var(--tp-common-white);
    color: var(--tp-text-body);
}

@media (max-width: 767px) {
    .postbox-nav button i {
        height: 40px;
        width: 40px;
        line-height: 40px;
    }
}

.postbox-nav button i:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .sidebar__wrapper {
        padding-left: 0;
    }
}

.sidebar__widget {
    padding: 50px 30px 40px 30px;
    box-shadow: 0px 12px 10px rgba(238, 239, 240, 0.25);
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
}

.sidebar__widget-content ul li {
    margin-bottom: 15px;
    list-style: none;
}

.sidebar__widget-content ul li:last-child {
    margin-bottom: 0;
}

.sidebar__widget-content ul li a {
    color: var(--tp-text-secondary);
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #F2F5FA;
    padding: 9px 15px;
    -webkit-transition: all 0.2s ease-out 0s;
    -moz-transition: all 0.2s ease-out 0s;
    -ms-transition: all 0.2s ease-out 0s;
    -o-transition: all 0.2s ease-out 0s;
    transition: all 0.2s ease-out 0s;
}

.sidebar__widget-content ul li a:hover {
    color: var(--tp-text-primary);
}

.sidebar__widget-content ul li a span {
    display: block;
    text-align: end;
}

.sidebar__widget-title {
    font-size: 22px;
    font-weight: 600;
    color: var(--tp-text-body);
}

.sidebar__search-input-2 input {
    height: 60px;
    width: 100%;
    padding: 10px 60px 10px 20px;
    border: none;
    background-color: var(--tp-grey-8);
}

.sidebar__search-input-2 input::placeholder {
    font-weight: 500;
    font-size: 12px;
    color: var(--tp-text-secondary);
    background-color: var(--tp-grey-8);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar__search-input-2 button {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    padding: 16px 25px;
}

.rc__post-thumb {
    margin-right: 20px;
}

.rc__post-thumb img {
    height: 100px;
    width: 100px;
    object-fit: cover;
}

.rc__meta span {
    font-weight: 600;
    font-size: 13px;
    color: var(--tp-text-secondary);
    letter-spacing: 1px;
    display: block;
    margin-bottom: 5px;
}

.rc__post-title {
    font-weight: 500;
    font-size: 16px;
    color: #141515;
    line-height: 1.5;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
(max-width: 767px) {
    .rc__post-title {
        font-size: 15px;
    }
}

.rc__post-title a {
    -webkit-transition: all 0.2s ease-out 0s;
    -moz-transition: all 0.2s ease-out 0s;
    -ms-transition: all 0.2s ease-out 0s;
    -o-transition: all 0.2s ease-out 0s;
    transition: all 0.2s ease-out 0s;
}

.rc__post-title a:hover {
    color: var(--tp-text-primary);
}

.tagcloud a {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: var(--tp-text-secondary);
    border: 1px solid #F2F4F6;
    padding: 10px 26px;
    display: inline-block;
    line-height: 1;
    margin: 5px 10px 5px 0;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tagcloud a:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-text-primary);
}

/*----------------------------------------*/
/*  12. CTA CSS START
/*----------------------------------------*/
.tpcta {
    padding: 58px 60px;
    border-radius: 6px;
    margin-left: 30px;
    position: relative;
    z-index: 1;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpcta {
        padding: 30px 20px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpcta {
        padding: 20px 15px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpcta {
        margin-left: 0;
    }
}

@media (max-width: 767px) {
    .tpcta {
        padding: 40px 20px;
        margin-left: 0;
    }
}

.tpcta::before {
    position: absolute;
    content: "";
    height: 75px;
    width: 75px;
    border-radius: 50%;
    background-color: var(--tp-common-white);
    z-index: -1;
    top: 40px;
    right: 135px;
}

.tpcta p {
    color: #9a9387;
    font-size: 14px;
}

.tpcta__subtitle {
    font-size: 20px;
    font-weight: 400;
    color: var(--tp-text-primary);
    margin-bottom: 5px;
}

.tpcta__title {
    font-size: 26px;
    font-weight: 600;
}

@media (max-width: 767px) {
    .tpcta__title {
        font-size: 22px;
    }
}

.tpcta__input-icon {
    position: absolute;
    top: 17px;
    left: 30px;
}

.tpcta__input-icon i {
    font-size: 16px;
}

.tpcta__input input {
    border: none;
    border-radius: 6PX;
    height: 60px;
    width: 100%;
    padding: 10px 30px 10px 52px;
}

.tpcta__input input::placeholder {
    color: #b0b0b0;
    font-size: 16px;
}

.tpcta__btn button {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
    font-size: 16px;
    font-weight: 700;
    width: 100%;
    padding: 17px 0;
    border-radius: 6px;
    margin-right: 15px;
}

.tpcta__btn button:hover i {
    animation: iconarrow 0.4s linear;
}

.tpcta__btn button span {
    margin-left: 15px;
}

.tptrack__item-icon {
    flex: 0 0 auto;
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;
    background-color: var(--tp-common-white);
    border-radius: 6px;
    margin-right: 20px;
}

.tptrack__thumb img {
    border-radius: 8px 8px 0 0;
    width: 100%;
}

.tptrack__content {
    padding: 50px;
    border-radius: 0 0 8px 8px;
}

@media (max-width: 767px) {
    .tptrack__content {
        padding: 20px 15px;
    }
}

.tptrack__item-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-text-body);
}

.tptrack__item-content p {
    font-size: 14px;
    color: var(--tp-text-secondary);
    line-height: 24px;
    margin-bottom: 0;
}

.tptrack__id span,
.tptrack__email span {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 30px;
    font-size: 16px;
}

.tptrack__id form,
.tptrack__email form {
    position: relative;
}

.tptrack__id form input,
.tptrack__email form input {
    width: 100%;
    height: 60px;
    border: none;
    background-color: var(--tp-common-white);
    padding: 10px 60px;
    border-radius: 6px;
}

.tptrack__id form input::placeholder,
.tptrack__email form input::placeholder {
    font-size: 14px;
    color: var(--tp-grey-9);
}

.tptrack__submition {
    background: var(--tp-text-primary);
    border-radius: 6px;
    color: var(--tp-common-white);
    display: inline-block;
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
    margin-bottom: 0;
    padding: 22px 50px;
    text-align: center;
    touch-action: manipulation;
    transition: all 0.3s ease 0s;
    vertical-align: middle;
    white-space: nowrap;
    width: 100%;
}

.tptrack__submition i {
    margin-left: 20px;
}

.tptrack__submition:hover {
    background-color: #b50c37;
}

/*----------------------------------------*/
/*  13. SERVICES CSS START
/*----------------------------------------*/
.tpservicesitem {
    border: 1px solid var(--tp-border-6);
    padding: 28px 30px;
    border-radius: 6px;
    background-color: var(--tp-common-white);
    position: relative;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpservicesitem {
        flex-direction: column;
        text-align: center;
        padding: 25px 15px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpservicesitem {
        flex-direction: column;
        text-align: center;
        padding: 25px 5px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpservicesitem {
        padding: 20px 10px;
    }
}

.tpservicesitem::before {
    position: absolute;
    content: "";
    height: 50%;
    width: 100%;
    background-color: var(--tp-text-primary);
    left: 0;
    bottom: 0;
    border-radius: 6px;
    z-index: -1;
}

.tpservicesitem:hover {
    border: 1px solid var(--tp-text-primary);
}

.tpservicesitem:hover::before {
    transform: rotate(3deg);
}

.tpservicesitem:hover .tpservicesitem__icon svg {
    transform: translate3d(0, -5px, 0);
}

.tpservicesitem__icon {
    flex: 0 0 auto;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpservicesitem__icon {
        margin-bottom: 15px;
        margin-right: 0;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpservicesitem__icon {
        margin-right: 11px;
    }
}

.tpservicesitem__icon svg {
    transform: translate3d(0, 0, 0);
    transition: transform 0.3s cubic-bezier(0.21, 0.6, 0.44, 2.18);
    height: 30px;
    width: 30px;
}

.tpservicesitem__content p {
    margin-bottom: 0;
    font-size: 14px;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpservicesitem__content p {
        font-size: 12px;
    }
}

.tpservicesitem__title {
    font-size: 18px;
    font-weight: 700;
    color: var(--tp-text-body);
    line-height: 24px;
    margin-bottom: 0;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .services-gx-item {
        --bs-gutter-x: 10px;
    }
}

.tpservices-border {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpservices-border .tpservicesitem__title {
    color: var(--tp-common-white);
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpservices-border p {
    color: var(--tp-text-5);
}

.tpservices-border:hover {
    background-color: var(--tp-common-white);
    border: 1px solid #fff;
}

.tpservices-border:hover .tpservicesitem__title {
    color: var(--tp-common-black);
}

/*----------------------------------------*/
/*  14. BRAND CSS START
/*----------------------------------------*/
.tpbrand .tpsection__title {
    color: var(--tp-common-white);
}

.tpbrand .left-line::before {
    background-color: var(--tp-border-5);
}

.tpbrand .right-line::after {
    background-color: var(--tp-border-5);
}

.tpbrand .brand-items img {
    opacity: 0.5;
}

.tpbrand .brand-items img:hover {
    opacity: 1;
}

.brand-items .swiper-slide {
    text-align: center;
}

@media (max-width: 767px) {
    .black-bg-brand {
        padding-top: 20px;
    }
}

.brand-items img {
    opacity: 0.5;
}

.brand-items img:hover {
    opacity: 1;
}

.tpabout__inner-title-area p {
    font-size: 18px;
    color: var(--tp-text-secondary);
    margin-bottom: 40px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpabout__inner-title-area p {
        font-size: 16px;
        margin-bottom: 15px;
    }
}

@media (max-width: 767px) {
    .tpabout__inner-title-area p {
        font-size: 16px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpabout__inner-title-area p {
        font-size: 16px;
        margin-bottom: 20px;
    }
}

.tpabout__logo {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
}

@media (max-width: 767px) {
    .tpabout__logo {
        bottom: 30px;
    }
}

.tpabout__logo img {
    max-width: 100%;
}

.tpabout__inner-thumb img {
    border-radius: 6px;
    max-width: 100%;
}

.tpabout__inner-title {
    font-size: 40px;
    color: var(--tp-text-body);
    font-weight: 600;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
(max-width: 767px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpabout__inner-title {
        font-size: 30px;
    }
}

.tpabout__inner-sub-title {
    font-size: 14px;
    margin-bottom: 5px;
    color: var(--tp-text-secondary);
}

.tpabout__inner-story p {
    font-size: 18px;
    color: var(--tp-text-secondary);
    margin-bottom: 0;
    margin-right: -25px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpabout__inner-story p {
        margin-right: 0px;
        font-size: 16px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpabout__inner-story p {
        margin-right: 0px;
        font-size: 16px;
    }
}

.tpabout__inner-story-2 p {
    font-size: 18px;
    color: var(--tp-text-secondary);
    margin-bottom: 0;
    margin-left: 75px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpabout__inner-story-2 p {
        margin-left: 35px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpabout__inner-story-2 p {
        margin-left: 0px;
        font-size: 16px;
    }
}

.tpabout__inner-list ul li {
    list-style: none;
    position: relative;
    padding-left: 45px;
    margin-bottom: 21px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpabout__inner-list ul li {
        padding-left: 35px;
    }
}

.tpabout__inner-list ul li:last-child {
    margin-bottom: 0;
}

.tpabout__inner-list ul li a {
    font-size: 18px;
    font-weight: 400;
    color: var(--tp-text-body);
    background-image: linear-gradient(#040404, #040404), linear-gradient(#040404, #040404);
    display: inline;
    background-size: 0% 1px, 0 1px;
    background-position: 100% 100%, 0 90%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
    line-height: 1.4;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpabout__inner-list ul li a {
        font-size: 15px;
    }
}

@media (max-width: 767px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpabout__inner-list ul li a {
        font-size: 16px;
    }
}

.tpabout__inner-list ul li a:hover {
    background-size: 0 1px, 100% 1px;
}

.tpabout__inner-list ul li a i {
    position: absolute;
    top: 5px;
    left: 10px;
    color: var(--tp-text-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .about-inner-content {
        margin-left: 0;
        margin-right: 0;
    }
}

/*----------------------------------------*/
/*  15. PLATINAM-PRODUCT CSS START
/*----------------------------------------*/
.tpratingitem__title {
    font-size: 15px;
    color: var(--tp-text-secondary);
}

.tpratingitem__title a:hover {
    color: var(--tp-text-primary);
}

.tpratingitem__content span {
    color: var(--tp-text-body);
    font-size: 16px;
    font-weight: 600;
}

.tpratingitem__star i {
    font-size: 12px;
    color: var(--tp-text-primary);
}

.tpratingitem__star span {
    font-size: 12px;
    font-weight: 400;
}

.tpratingitem__oldprice {
    color: #d3d2d2;
    font-weight: 600;
    font-style: 16px;
}

.tpratingitem__available {
    margin: 0 40px;
}

.tpratingitem__available .progress {
    height: 4px;
    background-color: #f0f0f0;
}

.tpratingitem__available .progress .progress-bar {
    width: 40% !important;
    background-color: var(--tp-text-primary);
    border-radius: 6px;
}

.tpratingitem__stock span {
    font-size: 14px;
    font-weight: 600;
}

.tpratingitem__stock span i {
    font-style: normal;
    font-size: 14px;
    color: #999999;
}

.tpratingitem__countdown span {
    display: inline-block;
    margin-bottom: 10px;
}

.tpratingitem__countdown span.cdown {
    color: var(--tp-text-body);
    font-size: 14px;
    font-weight: 600;
    margin-right: 13px;
    margin-bottom: 0;
    padding-right: 16px;
    position: relative;
}

.tpratingitem__countdown span.cdown::before {
    position: absolute;
    content: "";
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    height: 30px;
    width: 1px;
    background-color: #ededed;
}

.tpratingitem__countdown span.cdown:last-child::before {
    display: none;
}

.tpratingitem__countdown span.cdown p {
    margin-top: -15px;
    font-size: 16px;
    color: var(--tp-text-secondary);
}

.tpratingitem__count {
    display: flex;
    justify-content: center;
    margin-left: 30px;
}

.tpratingitem__count i {
    font-style: normal;
    font-size: 16px;
    margin-bottom: 0;
    padding-left: 13px;
    color: var(--tp-text-secondary);
}

@media (max-width: 767px) {
    .tpplatiarrow {
        justify-content: flex-start !important;
        margin-bottom: 20px;
    }
}

.tpratingitem__thumb {
    overflow: hidden;
}

.tpratingitem__thumb img {
    width: 100%;
}

.tpratingitem__thumb img:hover {
    transform: scale(1.08);
}

/*----------------------------------------*/
/*  16. FEATURES CSS START
/*----------------------------------------*/
.tpfeatures:hover .tpfeatures__icon img {
    transform: translate3d(0, -5px, 0);
}

.tpfeatures__icon img {
    transform: translate3d(0, 0, 0);
    transition: transform 0.3s cubic-bezier(0.21, 0.6, 0.44, 2.18);
}

.tpfeatures__icon {
    padding: 25px 24px;
    background-color: var(--tp-theme-13);
    display: inline-block;
    border-radius: 6px;
    width: 100px;
}

.tpfeatures__title {
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-text-9);
}

.tpfeaturesborder {
    position: relative;
}

.tpfeaturesborder::before {
    position: absolute;
    content: "";
    left: 0;
    top: 10px;
    height: 150px;
    width: 1px;
    background-color: #f2f2f2;
    display: none;
}

.tpfeaturesborder::after {
    position: absolute;
    content: "";
    right: -15px;
    top: 10px;
    height: 150px;
    width: 1px;
    background-color: #f2f2f2;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .tpfeaturesborder::after {
        display: none;
    }
}

.fea-row .col-lg-2:last-child .tpfeaturesborder::after {
    right: 0px;
}

.fea-row .col-lg-2:first-child .tpfeaturesborder::before {
    display: block;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .fea-row .col-lg-2:first-child .tpfeaturesborder::before {
        display: none;
    }
}

/*----------------------------------------*/
/*  17. TESTIMONIAL CSS START
/*----------------------------------------*/
.tptesti {
    background-color: #fcf6f4;
    padding: 40px 64px;
    border-radius: 6px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .tptesti {
        padding: 40px 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
(max-width: 767px) {
    .tptesti {
        padding: 30px 25px;
    }
}

.tptesti__icon {
    height: 50px;
    width: 50px;
    line-height: 50px;
    display: inline-block;
    background: #fff;
    text-align: center;
    border-radius: 50%;
}

.tptesti__content p {
    font-size: 20px;
    color: var(--tp-text-body);
    line-height: 1.6;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tptesti__content p {
        font-size: 17px;
    }
}

@media (max-width: 767px) {
    .tptesti__content p {
        font-size: 16px;
    }
}

.tptesti__avata-content-title {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 5px;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tptesti__avata-content-title {
        font-size: 14px;
    }
}

.tptesti__avata-content p {
    margin-bottom: 0;
    font-size: 14px;
    color: var(--tp-text-secondary);
}

@media (max-width: 767px) {
    .tptestiarrow {
        justify-content: flex-start !important;
        margin-bottom: 15px;
    }
}

.tpplatiarrow__prv,
.tpplatiarrow__nxt,
.tptestiarrow__prv,
.tptestiarrow__nxt {
    font-size: 16px;
    font-weight: 600;
    color: #c0c0c0;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpplatiarrow__prv:hover,
.tpplatiarrow__nxt:hover,
.tptestiarrow__prv:hover,
.tptestiarrow__nxt:hover {
    color: var(--tp-text-body);
}

.tpplatiarrow__nxt,
.tptestiarrow__nxt {
    margin-left: 20px;
}

.tpplatiarrow__nxt i,
.tptestiarrow__nxt i {
    margin-left: 12px;
}

.tpplatiarrow__prv i,
.tptestiarrow__prv i {
    margin-right: 12px;
}

.tp-white-testimonial .tptesti {
    background-color: var(--tp-common-white);
}

.tp-white-testimonial .tptesti__icon {
    background-color: var(--tp-text-primary);
}

.tp-white-testimonial .tptesti__content p {
    color: var(--tp-text-secondary);
}

.tp-white-testimonial .tptestiarrow__prv,
.tp-white-testimonial .tptestiarrow__nxt {
    font-size: 14px;
    font-weight: 600;
    color: var(--tp-text-body);
    background-color: var(--tp-common-white);
    padding: 6px 14px;
    line-height: 1;
    margin-right: 10px;
    border-radius: 6px;
    transition: all 0.3s ease-out 0s;
}

.tp-white-testimonial .tptestiarrow__prv:hover,
.tp-white-testimonial .tptestiarrow__nxt:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-text-primary);
}

.tp-white-testimonial .tptestiarrow__nxt {
    margin-right: 0;
    margin-left: 0;
}

.tpteam__thumb {
    position: relative;
}

.tpteam__thumb::before {
	transition: all 0.4s ease-out 0s;
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
    background-image: linear-gradient(90deg, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	background-image: -moz-linear-gradient(90deg, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	background-image: -webkit-linear-gradient(90deg, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	background-image: -ms-linear-gradient(90deg, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
	left: 0px;
	right: 0;
	bottom: 0px;
	z-index: 1;
	opacity: 0.6;
	border-radius: 6px;
}

.tpteam__thumb img {
    border-radius: 6px;
    width: 100%;
}

.tpteam__position {
    font-size: 14px;
    color: var(--tp-common-white);
    margin-bottom: 7px;
}

.tpteam__title {
    font-size: 24px;
    font-weight: 600;
    color: var(--tp-common-white);
    margin-bottom: 0;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpteam__title {
        font-size: 18px;
    }
}

.tpteam__content {
    position: absolute;
    bottom: 35px;
    z-index: 9;
    left: 38px;
}

.tpteam__btn {
    border: 1px solid var(--tp-border-1);
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-text-body);
    padding: 16px 33px;
    border-radius: 6px;
    display: inline-block;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpteam__btn {
        padding: 10px 33px;
        font-size: 16px;
    }
}

.tpteam__btn:hover {
    background-color: var(--tp-text-primary);
    border: 1px solid var(--tp-text-primary);
    color: var(--tp-common-white);
}

.tpfeature__inner-thumb img {
    max-width: 100%;
}

/*----------------------------------------*/
/*  18. SELECTPRODUCT CSS START
/*----------------------------------------*/
.tpselectproduct__heading {
    font-size: 20px;
    font-weight: 600;
}

.tpselectproduct__thumb {
    flex: 0 0 auto;
}

.tpselectproduct__thumb img {
    border-radius: 6px;
    width: 80px;
    height: 90px;
    object-fit: cover;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpselectproduct__rating {
    color: var(--tp-text-primary);
    font-size: 11px;
    margin-bottom: 3px;
}

.tpselectproduct__rating .max-star {
    color: #e0e0e0;
}

.tpselectproduct__title {
    font-size: 14px;
    font-weight: 400;
    color: var(--tp-text-secondary);
}

.tpselectproduct__title a:hover {
    color: var(--tp-text-primary);
}

.tpselectproduct__price {
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-text-body);
}

/*----------------------------------------*/
/*  19. PRODUCT-DETAILS CSS START
/*----------------------------------------*/
@media (max-width: 767px) {
    .tpproduct-details__nab {
        padding-right: 0;
    }
}

.tpproduct-details__nab .nav-link {
    padding: 0;
    border-color: transparent;
    margin-bottom: 20px;
}

.tpproduct-details__nab .nav-link img {
    height: 80px;
    width: 70px;
    object-fit: cover;
    border-radius: 6px;
    border-color: transparent;
}

.tpproduct-details__nab .nav-link.nav-link.active,
.tpproduct-details__nab .nav-link .nav-pills .show>.nav-link {
    color: #fff;
    background-color: transparent;
}

.tpproduct-details__nab .tab-pane img {
    max-width: 100%;
    border-radius: 6px;
}

.tpproduct-details__sticky {
    margin-left: -25px;
    position: sticky;
    top: 150px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpproduct-details__sticky {
        margin-left: -50px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .tpproduct-details__sticky {
        margin-left: 0px;
        margin-top: 20px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpproduct-details__sticky {
        margin-left: 0px;
        margin-top: 20px;
    }
}

.tpproduct-details__tag {
    display: inline-block;
    font-size: 14px;
    color: var(--tp-text-primary);
    font-weight: 600;
    line-height: 1;
    padding: 2px 8px 4px;
    background-color: rgba(213, 18, 67, 0.1);
    border-radius: 2px;
}

.tpproduct-details__rating {
    margin-left: 20px;
}

.tpproduct-details__rating a {
    font-size: 14px;
    color: var(--tp-common-yellow);
}

.tpproduct-details__rating a i {
    margin-left: -3px;
}

.tpproduct-details__reviewers {
    font-size: 14px;
    margin-left: 5px;
    color: var(--tp-text-secondary);
    cursor: pointer;
}

.tpproduct-details__reviewers:hover {
    color: var(--tp-text-primary);
}

.tpproduct-details__title {
    font-size: 26px;
    font-weight: 600;
    color: var(--tp-text-body);
    margin-right: 15px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpproduct-details__title {
        font-size: 20px;
    }
}

@media (max-width: 767px) {
    .tpproduct-details__title {
        font-size: 23px;
    }
}

.tpproduct-details__stock {
    font-size: 12px;
    font-weight: 600;
    color: var(--tp-text-primary);
    border: 1px solid var(--tp-border-1);
    border-radius: 2px;
    padding: 4px 9px;
    line-height: 1;
}

.tpproduct-details__price del {
    font-size: 20px;
    color: var(--tp-grey-6);
    font-weight: 600;
}

.tpproduct-details__price span {
    color: var(--tp-text-primary);
    font-weight: 600;
    font-size: 36px;
    margin-left: 7px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
(max-width: 767px) {
    .tpproduct-details__price span {
        font-size: 26px;
    }
}

.tpproduct-details__pera p {
    font-size: 16px;
    color: var(--tp-text-secondary);
    margin-bottom: 23px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpproduct-details__pera p {
        font-size: 12px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .tpproduct-details__pera p br {
        display: none;
    }
}

.tpproduct-details__quantity {
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
    display: inline-block;
    padding: 16px 28px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .tpproduct-details__quantity {
        padding: 11px 23px;
    }
}

@media (max-width: 767px) {
    .tpproduct-details__quantity {
        padding: 11px 10px;
    }
}

.tpproduct-details__quantity input {
    border: none;
    width: 25px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
}

.tpproduct-details__quantity .cart-minus,
.tpproduct-details__quantity .cart-plus {
    color: var(--tp-grey-7);
    cursor: pointer;
}

@media (max-width: 767px) {
    .tpproduct-details__cart {
        margin-left: 10px;
    }
}

.tpproduct-details__cart button {
    font-size: 16px;
    font-weight: 600;
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
    display: inline-block;
    padding: 22px 36px;
    line-height: 1;
    border-radius: 6px;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tpproduct-details__cart button:hover {
    background-color: var(--tp-text-body);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .tpproduct-details__cart button {
        padding: 17px 20px;
    }
}

@media (max-width: 767px) {
    .tpproduct-details__cart button {
        padding: 18px 14px;
        font-size: 13px;
    }
}

.tpproduct-details__wishlist {
    height: 60px;
    width: 60px;
    text-align: center;
    border: 1px solid var(--tp-border-1);
    display: inline-block;
    line-height: 60px;
    color: var(--tp-grey-7);
    border-radius: 6px;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .tpproduct-details__wishlist {
        height: 50px;
        width: 50px;
        line-height: 50px;
    }
}

@media (max-width: 767px) {
    .tpproduct-details__wishlist {
        height: 50px;
        width: 50px;
        line-height: 50px;
        margin-left: 10px;
    }
}

.tpproduct-details__wishlist:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-text-primary);
    border: 1px solid var(--tp-text-primary);
}

.tpproduct-details__wishlist a {
    display: block;
}

.tpproduct-details__information p {
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-text-body);
    display: inline-block;
    margin-right: 5px;
    margin-bottom: 5px;
}

.tpproduct-details__information span {
    color: var(--tp-text-secondary);
    font-weight: 400;
    font-size: 16px;
}

.tpproduct-details__information span a:hover {
    color: var(--tp-text-primary);
}

.tpproduct-details__information a {
    color: var(--tp-text-secondary);
    margin-right: 12px;
    font-size: 16px;
}

.tpproduct-details__information a:hover {
    color: var(--tp-text-primary);
}

.tpproduct-details__condation {
    margin-left: -60px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tpproduct-details__condation {
        margin-left: 0px;
    }
}

@media (max-width: 767px) {
    .tpproduct-details__condation {
        margin-top: 40px;
        margin-left: 0px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpproduct-details__condation {
        margin-top: 40px;
        margin-left: 0px;
    }
}

.tpproduct-details__condation ul li {
    list-style: none;
    margin-bottom: 20px;
}

.tpproduct-details__condation-item {
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
    padding: 11px 25px;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpproduct-details__condation-item {
        padding: 11px 8px;
    }
}

.tpproduct-details__condation-item:hover .tpproduct-details__img-hover {
    transform: translate3d(0, -5px, 0);
}

.tpproduct-details__img-hover {
    margin-right: 20px;
    flex: 0 0 auto;
    transform: translate3d(0, 0, 0);
    transition: transform 0.3s cubic-bezier(0.21, 0.6, 0.44, 2.18);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpproduct-details__img-hover {
        margin-right: 10px;
    }
}

.tpproduct-details__condation-text p {
    font-size: 14px;
    margin-bottom: 0;
    color: var(--tp-text-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpproduct-details__condation-text p br {
        display: none;
    }
}

.tpproduct-details__nav .nav-tabs {
    border-bottom: 1px solid var(--tp-border-1);
    display: flex;
    justify-content: center;
    padding-bottom: 15px;
}

.tpproduct-details__nav .nav-tabs .nav-links {
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-text-secondary);
    margin: 0 32px;
}

@media (max-width: 767px) {
    .tpproduct-details__nav .nav-tabs .nav-links {
        font-size: 14px;
        margin: 0 12px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpproduct-details__nav .nav-tabs .nav-links {
        margin: 0 15px;
    }
}

.tpproduct-details__nav .nav-tabs .nav-links.active {
    color: var(--tp-text-body);
}

.tpproduct-details__list-img {
    padding-right: 60px;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpproduct-details__list-img {
        padding-right: 0;
    }
}

.tpproduct-details__list-img-item {
    margin-bottom: 10px;
}

.tpproduct-details__list-img-item img {
    max-width: 100%;
    border-radius: 6px;
}

.latest-comments ul li {
    list-style: none;
}

.comments-box {
    margin-bottom: 50px;
}

.user-rating ul li {
    display: inline-block;
    color: var(--tp-common-yellow);
}

.comments-text span,
.comments-text p {
    color: var(--tp-text-secondary);
    font-size: 16px;
}

.comment-title p {
    color: var(--tp-text-secondary);
    font-size: 16px;
}

.comment-rating span {
    font-weight: 500;
    margin-right: 5px;
}

.comment-rating ul li {
    display: inline-block;
    color: var(--tp-common-yellow);
}

.comment-input textarea {
    width: 100%;
    height: 120px;
    margin-bottom: 20px;
    border: 1px solid #e7e7e7;
    padding: 20px;
}

.comment-input textarea:focus-visible {
    outline: 0;
    border: 1px solid var(--tp-text-primary) !important;
}

.comment-input input {
    width: 100%;
    height: 50px;
    resize: none;
    outline: 0;
    border: 1px solid #e7e7e7;
    padding: 8px 20px;
    margin-bottom: 30px;
}

.comment-input input:focus {
    border: 1px solid var(--tp-text-primary);
}

.comments-avatar {
    flex: 0 0 auto;
}

.table .add-info {
    font-size: 18px;
    font-weight: 500;
}

.table td {
    padding: 18px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px),
only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .shop-left-right {
        margin-left: 0;
        margin-right: 0;
    }
}

.tp-comments-title {
    font-size: 24px;
}

@media (max-width: 767px) {
    .tp-comments-title {
        font-size: 18px;
    }
}

.tp-content-tab p {
    font-size: 16px;
    color: var(--tp-text-secondary);
}

.pro-submit {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

@media (max-width: 767px) {
    .tprelated__arrow {
        justify-content: flex-start !important;
    }
}

.tprelated__arrow i {
    height: 50px;
    width: 50px;
    line-height: 50px;
    text-align: center;
    background-color: var(--tp-grey-2);
    border-radius: 6px;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.tprelated__arrow i:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

.tprelated__prv {
    margin-right: 15px;
}

.related-product-border {
    border-top: 1px solid var(--tp-border-1);
}

.comments-text {
    width: 100%;
}

/*----------------------------------------*/
/*  20. CART CSS START
/*----------------------------------------*/
.table-content table {
    background: var(--tp-common-white);
    border-color: var(--tp-border-1);
    border-radius: 0;
    border-style: solid;
    border-width: 1px 0 0 1px;
    text-align: center;
    width: 100%;
    margin-bottom: 0;
}

.table-content table.table> :not(:first-child) {
    border-top: 0;
}

.table-content .table> :not(:last-child)> :last-child>* {
    border-bottom-color: var(--tp-border-1);
}

.table-content .product-quantity {
    float: none;
}

.table-content table td.product-name {
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
}

.table-content table td.product-name a:hover {
    color: var(--tp-text-primary);
}

.table-content table td {
    border-top: medium none;
    padding: 20px 10px;
    vertical-align: middle;
    font-size: 16px;
}

.table-content table th,
.table-content table td {
    border-bottom: 1px solid var(--tp-border-1);
    border-right: 1px solid var(--tp-border-1);
}

.product-quantity input {
	color: #000;
	font-size: 18px;
	font-weight: 600;
	border: 1px solid var(--tp-border-1);
	height: 60px;
	border-radius: 4px;
	padding: 0 15px;
}

.table td,
.table th {
    border-top: 1px solid var(--tp-border-1);
}

.product-quantity>input {
    width: 80px;
    border-radius: 3px;
}

.table-content table td.product-subtotal {
    font-size: 16px;
}

.table-content table td .cart-plus-minus {
    float: none;
    margin: 0 auto;
}

.coupon-all {
    margin-top: 50px;
}

.coupon {
    float: left;
}

@media (max-width: 767px) {
    .coupon {
        float: none;
    }
}

#coupon_code {
    height: 52px;
    border: 1px solid var(--tp-border-1);
    padding: 0 15px;
    margin-right: 10px;
    border-radius: 8px;
}

@media (max-width: 767px) {
    #coupon_code {
        margin-bottom: 15px;
    }
}

.coupon2 {
    float: right;
}

@media (max-width: 767px) {
    .coupon2 {
        float: none;
        margin-top: 15px;
    }
}

.cart-page-total {
    padding-top: 50px;
}

.cart-page-total>h2 {
    font-size: 25px;
    margin-bottom: 20px;
    text-transform: capitalize;
}

.cart-page-total>ul {
    border: 1px solid var(--tp-border-1);
}

.cart-page-total>ul>li {
    list-style: none;
    font-size: 15px;
    color: #6f7172;
    padding: 10px 30px;
    border-bottom: 1px solid var(--tp-border-1);
    font-weight: 400;
}

.cart-page-total ul>li>span {
    float: right;
}

.cart-page-total li:last-child {
    border-bottom: 0;
}

td.product-thumbnail img {
    width: 125px;
}

.product-quantity .cart-plus,
.product-quantity .cart-minus {
    width: 25px;
    height: 30px;
    border: 1px solid var(--tp-border-1);
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    font-size: 14px;
}

.product-quantity .cart-plus:hover,
.product-quantity .cart-minus:hover {
    cursor: pointer;
    color: var(--tp-common-white);
    background: var(--tp-text-primary);
    border-color: var(--tp-text-primary);
}

.cart-input {
    height: 30px;
    width: 32px;
    text-align: center;
    font-size: 14px;
    border: none;
    border-top: 2px solid var(--tp-border-1);
    border-bottom: 2px solid var(--tp-border-1);
    display: inline-block;
    vertical-align: middle;
    margin: 0 -3px;
    padding-bottom: 0px;
}

.tp-wish-cart {
    min-width: 150px;
}

/* Checkout */
.coupon-accordion h3 {
    background-color: #f6f6f6;
    border-top: 3px solid rgba(213, 18, 67, 0.3);
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 25px;
    padding: 1em 2em 1em 3.5em;
    position: relative;
    width: auto;
}

.coupon-accordion h3::before {
    content: "\f07b";
    left: 15px;
    top: 13px;
    position: absolute;
    color: #6f7172;
    font-family: "Font Awesome 5 Pro";
    font-weight: 700;
}

.coupon-accordion span {
    color: #6f7172;
    cursor: pointer;
    transition: 0.3s;
    font-weight: 500;
}

.coupon-accordion span:hover,
p.lost-password a:hover {
    color: var(--tp-theme-primary);
}

.coupon-content {
    border: 1px solid #eaedff;
    display: none;
    margin-bottom: 20px;
    padding: 30px;
}

.coupon-info p.coupon-text {
    margin-bottom: 15px;
}

.coupon-info p {
    margin-bottom: 0;
}

.coupon-info p.form-row-first label,
.coupon-info p.form-row-last label {
    display: block;
    color: #6f7172;
}

.coupon-info p.form-row-first label span.required,
.coupon-info p.form-row-last label span.required {
    color: red;
    font-weight: 700;
}

.coupon-info p.form-row-first input,
.coupon-info p.form-row-last input {
    border: 1px solid #eaedff;
    height: 45px;
    margin: 0 0 14px;
    max-width: 100%;
    padding: 0 0 0 10px;
    width: 100%;
    outline: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}

.coupon-info p.form-row-first input:focus,
.coupon-info p.form-row-last input:focus {
    border-color: var(--tp-text-primary);
}

.coupon-info p.form-row input[type=submit]:hover,
p.checkout-coupon input[type=submit]:hover {
    background: #3e976c none repeat scroll 0 0;
}

.coupon-info p.form-row input[type=checkbox] {
    position: relative;
    top: 2px;
    transform: translateY(-3px);
}

.form-row>label {
    margin-top: 15px;
    margin-left: 15px;
    color: #6f7172;
}

.buttons-cart input,
.coupon input[type=submit],
.buttons-cart a,
.coupon-info p.form-row input[type=submit] {
    background: #252525 none repeat scroll 0 0;
    border: medium none;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    margin-right: 15px;
    padding: 0 15px;
    text-transform: uppercase;
    transition: all 0.3s ease 0s;
}

p.lost-password {
    margin-top: 15px;
}

p.lost-password a {
    color: #6f6f6f;
}

p.checkout-coupon input[type=text] {
    height: 45px;
    padding: 0 15px;
    width: 100%;
    border: 1px solid #eaedff;
    margin-bottom: 15px;
    outline: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}

p.checkout-coupon input[type=text]:focus {
    border-color: var(--tp-text-primary);
}

.coupon-checkout-content {
    display: none;
}

.checkbox-form h3 {
    border-bottom: 1px solid #eaedff;
    font-size: 26px;
    margin: 0 0 20px;
    padding-bottom: 10px;
    width: 100%;
}

.country-select {
    margin-bottom: 30px;
    position: relative;
}

.country-select select {
    width: 100%;
    background-color: transparent;
    border: 1px solid #eaedff;
    padding: 0 10px;
    height: 50px;
}

.country-select label,
.checkout-form-list label {
    color: #6f7172;
    display: block;
    margin: 0 0 5px;
}

.country-select label span.required,
.checkout-form-list label span.required {
    color: red;
}

.country-select .nice-select {
    border: 1px solid #eaedff;
    height: 45px;
    padding-left: 10px;
    width: 100%;
    color: #6f7172;
    margin-bottom: 20px;
}

.country-select .nice-select .list {
    width: 100%;
}

.checkout-form-list {
    margin-bottom: 30px;
}

.checkout-form-list label {
    color: #6f7172;
}

.checkout-form-list input[type=text],
.checkout-form-list input[type=password],
.checkout-form-list input[type=email] {
    background: #ffffff;
    border: 1px solid #eaedff;
    border-radius: 0;
    height: 45px;
    padding: 0 0 0 10px;
    width: 100%;
    outline: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}

.checkout-form-list input[type=text]:focus,
.checkout-form-list input[type=password]:focus,
.checkout-form-list input[type=email]:focus {
    border-color: var(--tp-text-primary);
}

.checkout-form-list input[type=text]::-moz-placeholder,
.checkout-form-list input[type=password]::-moz-placeholder,
.checkout-form-list input[type=email]::-moz-placeholder {
    color: #6f7172;
    opacity: 1;
}

.checkout-form-list input[type=text]::placeholder,
.checkout-form-list input[type=password]::placeholder,
.checkout-form-list input[type=email]::placeholder {
    color: #6f7172;
    opacity: 1;
}

.checkout-form-list input[type=checkbox] {
    display: inline-block;
    margin-right: 10px;
    position: relative;
    top: 1px;
    transform: translateY(-3px);
}

.create-acc label {
    color: #6f7172;
    display: inline-block;
}

.create-account {
    display: none;
}

.ship-different-title h3 label {
    display: inline-block;
    margin-right: 20px;
    color: #6f7172;
}

.order-notes textarea {
    border: 1px solid #eaedff;
    height: 120px;
    padding: 15px;
    width: 100%;
    outline: 0;
    resize: none;
}

.order-notes textarea:focus {
    border-color: var(--tp-text-primary);
}

.order-notes textarea::-moz-placeholder {
    color: #6f7172;
    opacity: 1;
}

.order-notes textarea::placeholder {
    color: #6f7172;
    opacity: 1;
}

#ship-box-info {
    display: none;
}

.panel-group .panel {
    border-radius: 0;
}

.panel-default>.panel-heading {
    border-radius: 0;
}

.your-order {
    padding: 30px 40px 45px;
    border: 3px solid rgba(213, 18, 67, 0.3);
}

@media (max-width: 767px) {
    .your-order {
        padding: 15px;
    }
}

.your-order h3 {
    border-bottom: 1px solid #eaedff;
    font-size: 30px;
    margin: 0 0 20px;
    padding-bottom: 10px;
    width: 100%;
}

.your-order-table table {
    background: none;
    border: 0;
    width: 100%;
}

.your-order-table table th,
.your-order-table table td {
    border-bottom: 1px solid #eaedff;
    border-right: medium none;
    color: #6f7172;
    font-size: 14px;
    padding: 15px 0;
    text-align: left;
}

@media (max-width: 767px) {

    .your-order-table table th,
    .your-order-table table td {
        padding-right: 10px;
    }
}

.your-order-table table th {
    border-top: medium none;
    color: #6f7172;
    font-weight: normal;
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    width: 250px;
}

.panel-body>p {
    color: #222;
}

.your-order-table table .shipping ul li {
    list-style: none;
}

.your-order-table table .shipping ul li input {
    position: relative;
    top: 2px;
}

.your-order-table table .shipping ul li label {
    color: #6f7172;
}

.your-order-table table .shipping th {
    vertical-align: top;
}

.your-order-table table .order-total th {
    border-bottom: 0;
    font-size: 14px;
}

.your-order-table table .order-total td {
    border-bottom: medium none;
}

.your-order-table table tr.cart_item:hover {
    background: #F9F9F9;
}

.your-order-table table tr.order-total td span {
    color: var(--tp-theme-primary);
    font-size: 18px;
    font-weight: 500;
}

.payment-method {
    margin-top: 40px;
}

.payment-method .accordion-item:last-of-type {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.payment-method .accordion-item {
    background-color: #fff;
    border: 0;
    border-bottom: 1px solid var(--tp-border-primary);
}

.payment-method .accordion-button {
    font-size: 16px;
    font-weight: 500;
    color: var(--tp-theme-primary);
    padding: 23px 0;
    border: none;
}

.payment-method .accordion-button:focus {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}

.payment-method .accordion-button::after {
    position: absolute;
    content: "\f067";
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    font-family: var(--tp-ff-fontawesome);
    font-size: 16px;
    font-weight: 400;
    margin-left: 0;
    background-image: none;
}

.payment-method .accordion-button:not(.collapsed) {
    color: var(--tp-theme-primary);
    background-color: var(--tp-common-white);
    box-shadow: none;
}

.payment-method .accordion-button:not(.collapsed)::after {
    content: "\f068";
}

.payment-method .accordion-body {
    padding: 8px 0;
    padding-bottom: 40px;
}

.payment-method .accordion-collapse {
    border: none;
}

.panel-title>a {
    display: block;
}

.order-button-payment input {
    background: #232323 none repeat scroll 0 0;
    border: medium none;
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    height: 40px;
    margin: 0px 0 0;
    padding: 0;
    text-transform: uppercase;
    transition: all 0.3s ease 0s;
    width: 100%;
}

.order-button-payment input:hover {
    background: #3e976c none repeat scroll 0 0;
}

.payment-method .btn-link {
    background: no-repeat;
    border: medium none;
    border-radius: 0;
    color: #444;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 1px;
    line-height: 1;
    margin-bottom: 0;
    padding: 3px 10px;
    text-align: center;
    text-transform: uppercase;
    transition: all 0.3s ease 0s;
    vertical-align: middle;
    white-space: nowrap;
    text-decoration: none;
}

.payment-method .card {
    background-color: #ffffff;
    border: 1px solid #eaedff;
    border-radius: 0;
    margin-bottom: 10px;
}

.payment-method .accordion .card:first-of-type {
    border: 1px solid #eaedff;
}

.card-header:first-child {
    border-radius: 0;
}

.payment-method .card-header {
    background-color: #ffffff;
    border-bottom: 1px solid #eaedff;
}

/*----------------------------------------*/
/*  21. LOCATION CSS START
/*----------------------------------------*/
.tpshop__location {
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
}

@media (max-width: 767px) {
    .tpshop__location {
        border-radius: 6px;
    }
}

.tpshop__content {
    padding: 10px 50px;
}

@media (max-width: 767px) {
    .tpshop__content {
        padding: 20px 12px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpshop__content {
        padding: 5px 20px;
    }
}

.tpshop__info ul li {
    list-style: none;
    position: relative;
    padding-left: 28px;
    margin-bottom: 15px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpshop__info ul li {
        margin-bottom: 7px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpshop__info ul li {
        margin-bottom: 10px;
    }
}

.tpshop__info ul li:last-child {
    margin-bottom: 0;
}

.tpshop__info ul li a {
    font-size: 16px;
    font-weight: 400;
    display: block;
    color: var(--tp-text-secondary);
    line-height: 26px;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpshop__info ul li a {
        font-size: 14px;
        line-height: 24px;
    }
}

.tpshop__info ul li a:hover {
    color: var(--tp-text-primary);
}

@media only screen and (min-width: 1400px) and (max-width: 1600px),
only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpshop__info ul li a br {
        display: none;
    }
}

.tpshop__info ul li span {
    font-size: 16px;
    font-weight: 400;
    display: block;
    color: var(--tp-text-secondary);
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpshop__info ul li span {
        font-size: 14px;
        line-height: 24px;
    }
}

.tpshop__info ul li i {
    position: absolute;
    top: 8px;
    left: 0;
    color: var(--tp-text-body);
    font-size: 14px;
}

.tpshop__thumb img {
    width: 100%;
}

.tpshop__title {
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-text-body);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpshop__title {
        font-size: 16px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tpshop__title {
        font-size: 16px;
        margin-bottom: 7px;
    }
}

.tpshop__location-map {
    height: 500px;
}

.tpshop__location-map iframe {
    width: 100%;
    height: 100%;
}

.tpshop__search {
    position: relative;
}

.tpshop__search form {
    position: relative;
}

.tpshop__search form input {
    height: 60px;
    width: 100%;
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
    padding: 5px 240px 5px 30px;
}

@media (max-width: 767px) {
    .tpshop__search form input {
        font-size: 12px;
        padding: 5px 150px 5px 15px;
        height: 48px;
    }
}

.tpshop__search form input::placeholder {
    font-size: 14px;
    color: var(--tp-grey-9);
}

.tpshop__search button {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-common-white);
    background-color: var(--tp-text-primary);
    border-radius: 0px 6px 6px 0px;
    padding: 17px 32px;
}

@media (max-width: 767px) {
    .tpshop__search button {
        font-size: 12px;
        padding: 10px 10px;
    }
}

.tpshop__search button i {
    margin-left: 10px;
}

.tpcontact__right {
    margin-right: 75px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tpcontact__right {
        margin-right: 20px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px),
only screen and (min-width: 576px) and (max-width: 767px) {
    .tpcontact__right {
        margin-right: 0px;
    }
}

.tpcontact__shop {
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
    padding: 45px 35px 45px 35px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tpcontact__shop {
        padding: 45px 15px 45px 15px;
    }
}

.tpcontact__shop .tpshop__title {
    font-size: 26px;
    font-weight: 600;
}

.tpcontact__title {
    font-size: 26px;
    font-weight: 600;
    color: var(--tp-text-body);
    margin-bottom: 0;
}

.tpcontact__info p {
    font-size: 14px;
    color: var(--tp-text-secondary);
}

.tpcontact__input input,
.tpcontact__input textarea {
    height: 60px;
    width: 100%;
    padding: 5px 20px;
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
}

.tpcontact__input input:focus,
.tpcontact__input textarea:focus {
    border: 1px solid var(--tp-text-primary);
    outline: 0;
}

.tpcontact__input input::placeholder,
.tpcontact__input textarea::placeholder {
    font-size: 16px;
    color: var(--tp-text-secondary);
}

.tpcontact__input textarea {
    height: 240px;
    padding: 25px;
}

.tpcontact__support a {
    display: block;
    font-weight: 600;
    font-size: 16px;
    border: 1px solid var(--tp-border-1);
    border-radius: 6px;
    padding: 16px 5px;
    text-align: center;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    margin-bottom: 10px;
}

.tpcontact__support a:hover {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
    border: 1px solid var(--tp-text-primary);
}

.tpcontact__support a:hover i {
    color: var(--tp-common-white);
}

.tpcontact__support a i {
    color: var(--tp-text-primary);
    margin-left: 14px;
}

.tperror__title {
    font-size: 48px;
    font-weight: 600;
    color: var(--tp-text-body);
}

.tperror__content p {
    font-size: 18px;
    font-weight: 400;
    color: var(--tp-text-secondary);
    margin-bottom: 40px;
}

.tp-error-btn i {
    margin-right: 15px;
}

/*----------------------------------------*/
/*  22. SIGN CSS START
/*----------------------------------------*/
.tpsign__account {
    margin-bottom: 18px;
}

.tpsign__account a {
    text-decoration: underline;
    font-size: 14px;
    font-weight: 400;
    color: var(--tp-text-body);
}

.tpsign__account a:hover {
    color: var(--tp-text-primary);
}

.tpsign__remember .form-check-input {
    border: none;
    background-color: var(--tp-common-white);
}

.tpsign__remember .form-check-input:checked {
    background-color: var(--tp-text-primary);
    border-color: transparent;
}

.tpsign__remember .form-check-input:focus {
    outline: 0;
    box-shadow: none;
}

.tpsign__remember .form-check-input[type=checkbox] {
    border-radius: 0;
    height: 15px;
    width: 15px;
    transform: translateY(2px);
}

.tpsign__remember .form-check-label {
    font-size: 14px;
    color: var(--tp-text-secondary);
}

.tpsign__pass a {
    font-size: 14px;
    color: var(--tp-text-body);
    text-decoration: underline;
}

.tpsign__reg {
    background-color: #f3eee7;
    color: var(--tp-common-black);
}

.tpsign__reg:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-text-body);
}

/*----------------------------------------*/
/*  23. COMING-SOON CSS START
/*----------------------------------------*/
.tpcoming__bg {
    background-repeat: no-repeat;
    background-size: contain;
}

.tpcoming__area {
    padding-top: 80px;
    padding-bottom: 280px;
}

.tpcoming__countdown {
    display: flex;
    align-items: center;
    justify-content: center;
}

.tpcoming__countdown span.cdown {
    background-color: transparent;
    height: 100%;
    width: 60px;
    text-align: center;
    color: var(--tp-common-white);
    font-size: 48px;
    font-weight: 700;
    margin-right: 85px;
    margin-bottom: 40px;
}

.tpcoming__countdown span p {
    margin-top: 10px;
    color: var(--tp-common-white);
}

.tpcoming__content {
    padding-top: 280px;
}

.tpcoming__content span {
    margin-bottom: 5px;
    display: inline-block;
    font-size: 18px;
    color: var(--tp-common-white);
}

.tpcoming__title {
    font-weight: 600;
    color: var(--tp-common-white);
    font-size: 48px;
}

.tpcoming__submit form {
    position: relative;
}

.tpcoming__submit form input {
    height: 60px;
    width: 100%;
    border: none;
    padding: 5px 200px 5px 55px;
}

.tpcoming__submit form input::placeholder {
    font-size: 14px;
    color: var(--tp-text-secondary);
}

.tpcoming__submit span {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 30px;
}

.tpcoming__submit button {
    position: absolute;
    font-size: 16px;
    color: var(--tp-common-white);
    font-weight: 600;
    right: -1px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--tp-text-primary);
    padding: 17px 20px;
}

.tpcoming__submit button i {
    margin-left: 11px;
}

/*----------------------------------------*/
/*  24. FOOTER CSS START
/*----------------------------------------*/
.footer-content p {
    color: var(--tp-text-secondary);
    font-size: 16px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .footer-content p br {
        display: none;
    }
}

.footer-widget__title {
    text-decoration: underline;
    font-size: 16px;
    font-weight: 600;
}

.footer-widget__links ul li {
    list-style: none;
    margin-bottom: 12px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .footer-widget__links ul li {
        margin-bottom: 8px;
    }
}

.footer-widget__links ul li:last-child {
    margin-bottom: 0;
}

.footer-widget__links ul li a {
    font-size: 18px;
    font-weight: 400;
    color: var(--tp-text-secondary);
    -webkit-transition: all 0.2s ease-out 0s;
    -moz-transition: all 0.2s ease-out 0s;
    -ms-transition: all 0.2s ease-out 0s;
    -o-transition: all 0.2s ease-out 0s;
    transition: all 0.2s ease-out 0s;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-widget__links ul li a {
        font-size: 15px;
    }
}

.footer-widget__links ul li a:hover {
    padding-left: 3px;
    color: var(--tp-text-primary);
}

.footer-widget__links ul li a:hover i {
    color: var(--tp-text-primary);
}

.footer-widget__links ul li a i {
    color: var(--tp-text-body);
    font-size: 18px;
    width: 35px;
    -webkit-transition: all 0.2s ease-out 0s;
    -moz-transition: all 0.2s ease-out 0s;
    -ms-transition: all 0.2s ease-out 0s;
    -o-transition: all 0.2s ease-out 0s;
    transition: all 0.2s ease-out 0s;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .footer-widget__links ul li a i {
        font-size: 14px;
        width: 25px;
    }
}

.footer-widget__newsletter input {
    height: 50px;
    width: 370px;
    border: none;
    padding: 0 28px;
    margin-bottom: 10px;
    border-radius: 6px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
    .footer-widget__newsletter input {
        width: 320px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .footer-widget__newsletter input {
        width: 280px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
(max-width: 767px) {
    .footer-widget__newsletter input {
        width: 100%;
    }
}

.footer-widget__newsletter input::placeholder {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
}

.main-footer {
    border-bottom: 1px solid var(--tp-border-4);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .footer-col-2 {
        margin-left: 0;
    }
}

.footer-col-4 {
    margin-left: -20px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .footer-col-4 {
        margin-left: -8px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .footer-col-4 {
        margin-left: 0;
    }
}

.footer-col-5 {
    margin-left: -30px;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .footer-col-5 {
        margin-left: 0;
    }
}

.footer-col-5 p {
    font-size: 18px;
    color: var(--tp-text-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .footer-col-5 p {
        font-size: 15px;
    }
}

.footer-cta__icon i {
    height: 50px;
    width: 50px;
    text-align: center;
    border-radius: 50%;
    line-height: 50px;
    background-color: var(--tp-common-white);
    font-size: 18px;
    display: block;
    margin-right: 15px;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
}

.footer-cta__contact {
    display: flex;
    align-items: center;
}

@media (max-width: 767px) {
    .footer-cta__contact {
        margin-bottom: 25px;
    }
}

.footer-cta__text a {
    display: block;
    font-size: 18px;
    font-weight: 700;
    line-height: 1;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-cta__text a {
        font-size: 15px;
    }
}

.footer-cta__text a:hover {
    color: var(--tp-text-primary);
}

.footer-cta__text span {
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    color: var(--tp-text-secondary);
}

.footer-cta__source {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

@media (max-width: 767px) {
    .footer-cta__source {
        display: inherit;
    }
}

.footer-cta__source-content {
    margin-right: 20px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-cta__source-content {
        margin-right: 15px;
    }
}

@media (max-width: 767px) {
    .footer-cta__source-content {
        margin-right: 0px;
        margin-bottom: 10px;
    }
}

.footer-cta__source-content p {
    color: var(--tp-text-secondary);
    font-size: 14px;
    margin-bottom: 0;
    line-height: 1;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-cta__source-content p {
        font-size: 12px;
    }
}

.footer-cta__source-title {
    font-weight: 700;
    font-size: 18px;
    line-height: 1;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-cta__source-title {
        font-size: 16px;
    }
}

.footer-cta__source-thumb a {
    margin-left: 4px;
}

.footer-cta__source-thumb a:first-child {
    margin-left: 0;
}

.footer-cta__source-thumb a img {
    border-radius: 6px;
}

.footer-copyright {
    padding: 22px 0 25px 0;
}

.footer-copyright__content span {
    font-size: 16px;
    color: var(--tp-text-secondary);
    margin-bottom: 0;
}

@media (max-width: 767px) {
    .footer-copyright__content span {
        display: block;
        margin-bottom: 12px;
    }
}

.footer-copyright__content span a {
    font-size: 16px;
    font-weight: 700;
    color: var(--tp-text-body);
}

.footer-copyright__content span a:hover {
    color: var(--tp-text-primary);
}

.footer-copyright__brand {
    text-align: right;
}

@media only screen and (min-width: 576px) and (max-width: 767px),
(max-width: 767px) {
    .footer-copyright__brand {
        text-align: center;
    }
}

@media (max-width: 767px) {
    .footer-copyright__brand img {
        max-width: 100%;
    }
}

.secondary-footer .main-footer {
    border-bottom: 1px solid var(--tp-border-5);
}

.secondary-footer p {
    color: var(--tp-text-4);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
    .secondary-footer p br {
        display: none;
    }
}

.secondary-footer .footer-widget__title {
    color: var(--tp-common-white);
}

.secondary-footer .footer-widget__links ul li a {
    color: var(--tp-text-4);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .secondary-footer .footer-widget__links ul li a {
        font-size: 16px;
    }
}

.secondary-footer .footer-widget__links ul li a:hover {
    color: var(--tp-text-primary);
}

.secondary-footer .footer-widget__links ul li a:hover i {
    color: var(--tp-text-primary);
}

.secondary-footer .footer-widget__links ul li a i {
    color: var(--tp-common-white);
}

.secondary-footer .footer-cta__icon i {
    background-color: var(--tp-text-body);
    color: var(--tp-common-white);
    border: 1px solid var(--tp-border-5);
}

.secondary-footer .footer-cta__text {
    color: var(--tp-common-white);
}

.secondary-footer .footer-cta__source-title {
    color: var(--tp-common-white);
}

.secondary-footer .footer-copyright__content span {
    color: var(--tp-text-4);
}

.secondary-footer .footer-copyright__content span a {
    color: var(--tp-common-white);
}

.secondary-footer .footer-copyright__content span a:hover {
    color: var(--tp-text-primary);
}

.secondary-footer .keyword a {
    color: var(--tp-text-4);
    padding-right: 18px;
    position: relative;
    font-size: 17px;
    margin-bottom: 10px;
    display: inline-block;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .secondary-footer .keyword a {
        font-size: 16px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .secondary-footer .keyword a {
        padding-right: 15px;
        font-size: 15px;
        margin-bottom: 2px;
    }
}

.secondary-footer .keyword a:hover {
    color: var(--tp-common-white);
    text-decoration: underline;
}

.secondary-footer .keyword a::before {
    position: absolute;
    content: "";
    height: 18px;
    width: 1px;
    background-color: #303030;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.platinam-footer .keyword a {
    color: var(--tp-text-secondary);
    padding-right: 18px;
    position: relative;
    font-size: 18px;
    margin-bottom: 10px;
    display: inline-block;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.platinam-footer .keyword a:hover {
    color: var(--tp-text-body);
    text-decoration: underline;
}

.platinam-footer .keyword a::before {
    position: absolute;
    content: "";
    height: 18px;
    width: 1px;
    background-color: #303030;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}

@media (max-width: 767px) {
    .footer-cta {
        margin-top: 10px;
        margin-bottom: 10px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .footer-4-col-4 {
        margin-left: 0;
    }
}


.input-range__slider {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: #fff;
    border: 2px solid var(--primary-color);
    border-radius: 100%;
    cursor: pointer;
    display: block;
    height: 17px;
    margin-left: -0.5rem;
    margin-top: -0.65rem;
    outline: none;
    position: absolute;
    top: 50%;
    transition: transform 0.3s ease-out, box-shadow 0.3s ease-out;
    width: 17px;
    position: absolute;
    left: 50%;
    top: 50%;
}

.input-range__slider:focus {
    box-shadow: none;
}

.input-range__slider:active {
    transform: scale(1.3);
}

.input-range__slider:focus {
    box-shadow: 0 0 0 5px rgba(63, 81, 181, 0.2);
}

.input-range--disabled .input-range__slider {
    background: #cccccc;
    border: 1px solid #cccccc;
    box-shadow: none;
    transform: none;
}

.input-range__slider-container {
    transition: left 0.3s ease-out;
}

.input-range__label {
    color: #aaaaaa;
    font-family: "Helvetica Neue", san-serif;
    font-size: 0.8rem;
    transform: translateZ(0);
    white-space: nowrap;
}

.input-range__label--min,
.input-range__label--max {
    bottom: -1.4rem;
    position: absolute;
}

.input-range__label--min {
    left: 0;
}

.input-range__label--max {
    right: 0;
}

.input-range__label--value {
    position: absolute;
    top: -1.8rem;
}

.input-range__label-container {
    left: -50%;
    position: relative;
}

.input-range__label--max .input-range__label-container {
    left: 50%;
}

.input-range__track {
    background: #d4e1f6;
    border-radius: 0.3rem;
    cursor: pointer;
    display: block;
    height: 5px;
    position: relative;
    transition: left 0.3s ease-out, width 0.3s ease-out;
}

.input-range--disabled .input-range__track {
    background: var(--primary-color);
}

.input-range__track--background {
    left: 0;
    margin-top: -0.15rem;
    position: absolute;
    right: 0;
    top: 50%;
}

.input-range__track--active {
    background: var(--primary-color);
}

.input-range {
    height: 1rem;
    position: relative;
    width: 100%;
}

.nft__filter-price-box {
    width: 47px;
    position: relative;
    display: flex;
    align-items: center;
}

.nft__filter-price-box input {
    width: 100%;
    background-color: #fff;
    border: none;
    padding-left: 0;
    padding-right: 0;
    pointer-events: none;
    color: #006c70;
    font-size: 16px;
    font-family: "Nunito";
    font-weight: bold;
}

.nft__filter-price-box span {
    color: #006c70;
    font-size: 16px;
    font-family: "Nunito";
    font-weight: bold;
}

.nft__filter-price-to span {
    color: #fff;
    opacity: 0.6;
    margin: 0 10px;
}

.pricing_acontent {
    .input-range {
        margin-top: 20px;
    }
}


/*# sourceMappingURL=main.css.map */