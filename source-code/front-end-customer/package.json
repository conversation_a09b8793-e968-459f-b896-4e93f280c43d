{"name": "start-app-dir", "version": "0.1.0", "private": true, "author": "alithemes", "scripts": {"dev": "next dev --port 9000", "dev:3000": "next dev --port 3000", "dev:5000": "next dev --port 5000", "dev:8000": "next dev --port 8000", "build": "next build", "start": "next start", "lint": "next lint", "sass": "sass --watch public/assets/scss/main.scss:public/assets/css/main.css"}, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "bootstrap": "^5.3.5", "next": "14.0.1", "qrcode.react": "^4.2.0", "react": "18.2.0", "react-bootstrap": "^2.10.9", "react-copy-to-clipboard": "^5.1.0", "react-dom": "18.2.0", "react-input-range": "^1.3.0", "react-modal-video": "^2.0.1", "react-redux": "^9.0.4", "react-toastify": "^9.1.3", "sass": "^1.69.5", "socket.io-client": "^4.7.2", "swiper": "^11.0.5", "wowjs": "^1.1.3"}}