'use client'
import { useState, useRef, useEffect } from 'react'
import Image from 'next/image'

const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  priority = false,
  quality = 75,
  placeholder = 'blur',
  blurDataURL,
  sizes,
  fill = false,
  objectFit = 'contain',
  objectPosition = 'center',
  loading = 'lazy',
  onLoad,
  onError,
  fallbackSrc = '/assets/img/product/placeholder.jpg',
  ...props
}) => {
  const [imgSrc, setImgSrc] = useState(src)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(priority) // If priority, load immediately
  const imgRef = useRef(null)

  // Generate a simple blur data URL for placeholder
  const generateBlurDataURL = () => {
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+'
  }

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [priority, isInView])

  const handleLoad = (e) => {
    setIsLoading(false)
    setHasError(false)
    if (onLoad) onLoad(e)
  }

  const handleError = (e) => {
    setIsLoading(false)
    setHasError(true)
    setImgSrc(fallbackSrc)
    if (onError) onError(e)
  }

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div 
      className={`loading-skeleton ${className}`}
      style={{
        ...style,
        backgroundColor: '#f0f0f0',
        background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
        backgroundSize: '200% 100%',
        animation: 'loading 1.5s infinite',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#ccc',
        fontSize: '12px'
      }}
    >
      <style jsx>{`
        @keyframes loading {
          0% { background-position: 200% 0; }
          100% { background-position: -200% 0; }
        }
      `}</style>
      Loading...
    </div>
  )

  // If not in view and not priority, show placeholder
  if (!isInView && !priority) {
    return (
      <div 
        ref={imgRef}
        className={className}
        style={{
          ...style,
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <LoadingSkeleton />
      </div>
    )
  }

  // Determine if we should use Next.js Image or regular img
  const shouldUseNextImage = imgSrc && !imgSrc.startsWith('/assets/') && !hasError

  if (shouldUseNextImage) {
    return (
      <div ref={imgRef} style={{ position: 'relative', ...style }} className={className}>
        {isLoading && <LoadingSkeleton />}
        <Image
          src={imgSrc}
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          fill={fill}
          sizes={sizes || (fill ? '100vw' : undefined)}
          quality={quality}
          priority={priority}
          placeholder={placeholder}
          blurDataURL={blurDataURL || generateBlurDataURL()}
          style={{
            objectFit,
            objectPosition,
            opacity: isLoading ? 0 : 1,
            transition: 'opacity 0.3s ease'
          }}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      </div>
    )
  }

  // Fallback to regular img tag for local assets
  return (
    <div ref={imgRef} style={{ position: 'relative', ...style }} className={className}>
      {isLoading && <LoadingSkeleton />}
      <img
        src={imgSrc}
        alt={alt}
        loading={loading}
        style={{
          width: fill ? '100%' : width,
          height: fill ? '100%' : height,
          objectFit,
          objectPosition,
          opacity: isLoading ? 0 : 1,
          transition: 'opacity 0.3s ease'
        }}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
    </div>
  )
}

export default OptimizedImage
