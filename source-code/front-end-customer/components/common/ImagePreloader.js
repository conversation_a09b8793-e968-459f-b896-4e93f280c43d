'use client'
import { useEffect } from 'react'

const ImagePreloader = ({ images = [], priority = false }) => {
  useEffect(() => {
    if (!images.length) return

    // Preload images
    const preloadImages = () => {
      images.forEach((src, index) => {
        if (src && !src.startsWith('/assets/')) {
          const link = document.createElement('link')
          link.rel = 'preload'
          link.as = 'image'
          link.href = src
          
          // Add priority for first few images
          if (priority && index < 3) {
            link.fetchPriority = 'high'
          }
          
          document.head.appendChild(link)
        }
      })
    }

    // Use requestIdleCallback if available, otherwise setTimeout
    if (typeof window !== 'undefined') {
      if (window.requestIdleCallback) {
        window.requestIdleCallback(preloadImages)
      } else {
        setTimeout(preloadImages, 100)
      }
    }
  }, [images, priority])

  return null // This component doesn't render anything
}

export default ImagePreloader
