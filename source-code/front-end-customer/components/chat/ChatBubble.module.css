.chatBubbleContainer {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  transition: bottom 0.3s ease;
}

.chatButton {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #4caf50;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  outline: none;
  position: absolute;
  bottom: 0;
  right: 0;
}

.chatButton:hover {
  background-color: #43a047;
  transform: scale(1.05);
}

.chatButtonActive {
  background-color: #e53935;
}

.chatButtonActive:hover {
  background-color: #d32f2f;
}

/* Registration Form Styles */
.registrationContainer {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.registrationHeader {
  text-align: center;
  margin-bottom: 20px;
}

.registrationHeader h4 {
  margin: 0 0 10px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.registrationHeader p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.registrationForm {
  display: flex;
  flex-direction: column;
}

.formGroup {
  margin-bottom: 15px;
}

.formGroup label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #e53935;
}

.formGroup input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
}

.formGroup input:focus {
  border-color: #4caf50;
}

.inputError {
  border-color: #e53935 !important;
}

.errorMessage {
  color: #e53935;
  font-size: 12px;
  margin-top: 5px;
}

.registrationButton {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 12px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 10px;
  transition: background-color 0.3s;
}

.registrationButton:hover {
  background-color: #43a047;
}

.chatWindow {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 350px;
  height: 500px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: slideIn 0.3s forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chatHeader {
  background-color: #4caf50;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chatHeader h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.chatStatus {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.statusDot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.statusOnline .statusDot {
  background-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
}

.statusOffline .statusDot {
  background-color: #FFC107;
  box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
}

.statusReconnecting .statusDot {
  background-color: #FF9800;
  box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.3);
  animation: blinkDot 1s infinite alternate;
}

@keyframes blinkDot {
  from {
    opacity: 0.4;
  }
  to {
    opacity: 1;
  }
}

.chatMessages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.chatWelcome {
  text-align: center;
  padding: 20px;
  color: #666;
}

.chatWelcomeIcon {
  font-size: 50px;
  color: #4caf50;
  margin-bottom: 15px;
}

.chatWelcome h4 {
  margin: 0 0 10px;
  font-size: 20px;
  font-weight: 600;
}

.chatWelcome p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.messageContainer {
  margin-bottom: 15px;
  max-width: 80%;
  clear: both;
}

.userMessage {
  float: right;
}

.adminMessage {
  float: left;
}

.systemMessage {
  width: 100%;
  text-align: center;
  margin: 15px 0;
}

.systemMessage .messageContent {
  display: inline-block;
  background-color: #f1f1f1;
  color: #666;
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-style: italic;
}

.messageSender {
  font-size: 12px;
  color: #666;
  margin-bottom: 3px;
  font-weight: 600;
}

.messageContent {
  padding: 10px 15px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
}

.userMessage .messageContent {
  background-color: #4caf50;
  color: white;
  border-top-right-radius: 2px;
}

.adminMessage .messageContent {
  background-color: white;
  color: #333;
  border-top-left-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.messageText {
  font-size: 14px;
  line-height: 1.4;
}

.messageTime {
  font-size: 10px;
  opacity: 0.8;
  margin-top: 3px;
  text-align: right;
}

.userMessage .messageTime {
  color: rgba(255, 255, 255, 0.8);
}

.typingIndicator {
  display: inline-flex;
  align-items: center;
  padding: 10px 15px;
}

.typingIndicator span {
  height: 8px;
  width: 8px;
  float: left;
  margin: 0 1px;
  background-color: #9E9E9E;
  display: block;
  border-radius: 50%;
  opacity: 0.4;
}

.typingIndicator span:nth-child(1) {
  animation: bounceAnimation 1s infinite;
}

.typingIndicator span:nth-child(2) {
  animation: bounceAnimation 1s infinite;
  animation-delay: 0.2s;
}

.typingIndicator span:nth-child(3) {
  animation: bounceAnimation 1s infinite;
  animation-delay: 0.4s;
}

@keyframes bounceAnimation {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
  100% {
    transform: translateY(0px);
  }
}

.chatError {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 8px 12px;
  border-radius: 5px;
  font-size: 13px;
  margin: 0 15px 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.chatSuccess {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 8px 12px;
  border-radius: 5px;
  font-size: 13px;
  margin: 0 15px 15px;
  display: flex;
  align-items: center;
}

.chatError i, .chatSuccess i {
  margin-right: 5px;
}

.errorText {
  flex: 1;
}

.reconnectButton {
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 8px;
  transition: background-color 0.3s;
}

.reconnectButton:hover {
  background-color: #b71c1c;
}

.debugStatus {
  background-color: #f5f5f5;
  color: #757575;
  font-size: 11px;
  text-align: center;
  padding: 3px 0;
  border-top: 1px solid #e0e0e0;
}

.chatInputContainer {
  display: flex;
  padding: 10px 15px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
}

.chatInput {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 10px 15px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
}

.chatInput:focus {
  border-color: #4caf50;
}

.chatInput:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.chatSendButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #4caf50;
  color: white;
  border: none;
  margin-left: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.chatSendButton:hover:not(:disabled) {
  background-color: #43a047;
  transform: scale(1.05);
}

.chatSendButton:disabled {
  background-color: #e0e0e0;
  cursor: not-allowed;
}

/* Responsive styles */
@media screen and (max-width: 480px) {
  .chatWindow {
    width: 300px;
    height: 450px;
    bottom: 70px;
  }
  
  .chatButton {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .chatBubbleContainer {
    bottom: 20px;
    right: 20px;
  }
  
  /* Full-screen chat on very small devices */
  @media screen and (max-width: 360px) {
    .chatWindow {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      max-height: 100vh;
      border-radius: 0;
      z-index: 9999;
    }
    
    .chatButton {
      z-index: 10000;
    }
    
    .chatButtonActive {
      position: fixed;
      top: 10px;
      right: 10px;
      background-color: transparent;
      box-shadow: none;
      color: #333;
    }
    
    .chatButtonActive:hover {
      background-color: transparent;
    }
    
    .chatHeader {
      border-radius: 0;
      padding: 15px 20px;
    }
    
    .chatInputContainer {
      padding: 15px 20px;
      border-radius: 0;
    }
  }
} 